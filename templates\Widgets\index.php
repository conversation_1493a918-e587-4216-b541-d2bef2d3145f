<?php

/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Widget> $widgets
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
        margin-bottom: 10px;
    }

    #status-filter {
        width: 200px;
    }
</style>
<?php $this->end(); ?>

<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style mb-0">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item active">
            <?= __('Widgets') ?>
        </li>
    </ul>
</div>

<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>

<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4><?= __('Manage Widgets') ?></h4>
                <div class="card-header-form d-flex align-items-center">
                    <form class="d-flex align-items-center m-l-10">
                        <div class="input-group me-2">
                            <input type="text" class="form-control search-control" placeholder="<?= __('Search') ?>"
                                id="customSearchBox" />
                            <div class="input-group-btn">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <?php if ($canAdd): ?>
                                <a href="<?= $this->Url->build(['controller' => 'Widgets', 'action' => 'add']) ?>"
                                    class="btn btn-primary me-2" style="
                                                        position: relative;
                                                        left: 10px;
                                                        height:10%
                                                    ">
                                    <i class="fas fa-plus"></i>
                                    <?= __('Add Widget') ?>
                                </a>
                            <?php endif; ?>
                            <button class="btn menu-toggle fw-bold" type="submit" style="
                                                        position: relative;
                                                        left: 26px;
                                                        height:10%;
                                                    ">
                                <i class="fas fa-filter"></i>
                                <?= __('Filter') ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <div id="filter-body-container">
                <div class="input-group m-l-25">
                    <form accept-charset="utf-8" class="form-inline filter-rating attribute" id="filter-search">
                        <div class="d-flex">

                            <div class="form-group d-flex align-items-center">
                                <?php echo $this->Form->control('status', [
                                    'type' => 'select',
                                    'options' => $status,
                                    'id' => 'status-filter',
                                    'class' => 'form-control form-select p-10',
                                    'label' => false,
                                    'empty' => __('Widget Status'),
                                    'data' => ['bs-toggle' => 'dropdown'],
                                    'aria-expanded' => 'false'
                                ]) ?>
                            </div>

                            <div class="form-group ms-4" style="margin-top:0.5%">

                                <button type="submit" id="btnFilter" class="btn btn-primary">
                                    <i class="fa fa-filter" aria-hidden="true"></i>
                                </button>
                                <button type="reset" id="btnReset" class="btn btn-primary">
                                    <i class="fas fa-redo-alt"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                    <hr />
                </div>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <div id="table-2_wrapper"
                        class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer ">
                        <table class="table table-striped dataTable no-footer display nowrap table-hover border" id="tblWidgets" role="grid" aria-describedby="tblBanners_info">
                            <thead>
                                <tr role="row">
                                    <th><?= __('Id') ?></th>
                                    <th><?= __('Display Order') ?></th>
                                    <th><?= __('Widget Title') ?></th>
                                    <th><?= __('Widget Title (Arabic)') ?></th>
                                    <th><?= __('Widget Type') ?></th>
                                    <th><?= __('Country') ?></th>
                                    <th><?= __('Status') ?></th>
                                    <th><?= __('Action') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                foreach ($widgets as $list): ?>
                                    <tr>
                                        <td><?= h($list->id ?? '') ?></td>
                                        <td><?= h($list->display_order ?? '') ?></td>
                                        <td><?= h($list->title ?? '') ?></td>
                                        <td><?= h($list->title_ar ?? '') ?></td>
                                        <td><?= h($list->widget_type ? $widgetTypes[$list->widget_type] : '') ?></td>
                                        <td><?= isset($list->country) && !empty($list->country->name) ? h($list->country->name) : '-' ?></td>
                                        <td>
                                            <?php
                                            $statusval = $statusMap[$list->status] ?? ['label' => 'Unknown', 'class' => 'label-danger'];
                                            ?>
                                            <div class="badge-outline <?= h($statusval['class'] ?? '') ?>">
                                                <?= h($statusval['label'] ?? '') ?>
                                            </div>
                                        </td>
                                        <td class="actions">
                                            <?php if ($canView): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Widgets', 'action' => 'view', $list->id]) ?>"
                                                    class="" data-toggle="tooltip" title="<?= __('View') ?>"><i
                                                        class="far fa-eye m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canEdit): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Widgets', 'action' => 'edit', $list->id]) ?>"
                                                    class="" data-toggle="tooltip" title="<?= __('Edit') ?>"><i
                                                        class="fas fa-pencil-alt m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canDelete): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Widgets', 'action' => 'delete', $list->id]) ?>"
                                                    class="delete-btn" data-bs-toggle="tooltip" title="Delete" data-id="<?= $list->id ?>" data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                                                    data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                                                    data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                                                    <i class="far fa-trash-alt"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>">
</script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js'); ?>"></script>

<script>
    $(document).ready(function() {
        var paginationCount = <?= json_encode($paginationCount) ?>;
        var hasActionsColumn = <?= ($canView || $canEdit || $canDelete) ? 'true' : 'false' ?>;

        var columnDefs = [];

        // Only add orderable: false for actions column if it exists
        if (hasActionsColumn) {
            columnDefs.push({
                orderable: false,
                targets: [-1]
            });
        }

        var table = $("#tblWidgets").DataTable({
            stateSave: false,
            columnDefs: columnDefs,
            order: [],
            dom: "rtip", // Remove the default search box
            pageLength: paginationCount,
            language: {
                infoFiltered: ""
            }
        });

        //table.column(3).search('Active', true, false, false).draw();

        $("#customSearchBox").on("keyup", function() {
            table.search(this.value).draw();
        });

        $("#btnReset").on("click", function() {
            $('#status-filter').val('');
            $('#customSearchBox').val('');
            table.search('').columns().search('').draw();
            // Reload data with default filters
            performSearch();
        });

        $("#btnFilter").on("click", function(event) {
            event.preventDefault();
            performSearch();
        });

        function performSearch() {
            var filterStatus = $('#status-filter').val();

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Widgets', 'action' => 'filterSearch']) ?>',
                type: 'GET',
                data: {
                    filterStatus: filterStatus
                },
                success: function(response) {
                    console.log('AJAX Response:', response);
                    console.log('Data structure:', response.data);
                    if (response.data && response.data.length > 0) {
                        console.log('First row:', response.data[0]);
                        console.log('Number of columns in first row:', response.data[0].length);
                    }
                    table.clear().rows.add(response.data).draw();
                },
                error: function(xhr, status, error) {
                    console.log('Error:', error);
                }
            });
        }
    });
</script>
<?php $this->end(); ?>
<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;
use Cake\Routing\Router;

/**
 * Brands Controller
 *
 * @property \App\Model\Table\BrandsTable $Brands
 */
class BrandsController extends AppController
{

    protected $Categories;
    protected $BrandCategoryMappings;
    protected $Countries;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Categories = $this->fetchTable('Categories');
        $this->BrandCategoryMappings = $this->fetchTable('BrandCategoryMappings');
        $this->Countries = $this->fetchTable('Countries');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }

    public function index()
    {
        $query = $this->Brands->find()
            ->select(['id', 'name', 'name_ar', 'brand_logo', 'status', 'country_id']) // Explicitly select name_ar and country_id
            ->contain([
                'Categories' => function ($q) {
                    return $q->select(['Categories.id', 'Categories.name']);
                },
                'Countries' => function ($q) {
                    return $q->select([
                        'Countries.id',
                        'Countries.name' // adjust field as per your table
                    ]);
                }
            ])->order(['Brands.name' => 'ASC']);

        $query->where(['Brands.status !=' => 'D']);

        // Apply role-based country filtering
        $query = $this->applyRoleBasedCountryFilter($query, 'Brands.country_id');

        $brands = $query->toArray();
        foreach ($brands as $brand) {
            $brand->brand_logo = $this->Media->getCloudFrontURL($brand->brand_logo);
        }
       
        $title = 'Manage Brands';
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $this->set(compact('brands', 'title', 'status', 'statusMap'));
    }

    /**
     * View method
     *
     * @param string|null $id Brand id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $brand = $this->Brands->get($id, [
            'contain' => [
                'BannerAds',
                'Categories',  // Fetch associated categories
                'Products'
            ]
        ]);

        $brand_logo = $this->Media->getCloudFrontURL($brand->brand_logo);
        $web_banner = $this->Media->getCloudFrontURL($brand->web_banner);

        $title = 'Brand | View';
        $this->set(compact('brand', 'brand_logo', 'web_banner', 'title'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {

        $this->set([
            'logoSize' => Configure::read('Constants.BRAND_LOGO_SIZE'),
            'bannerSize' => Configure::read('Constants.BRAND_BANNER_SIZE'),
            'logoType' => Configure::read('Constants.BRAND_LOGO_JS_TYPE'),
            'bannerType' => Configure::read('Constants.BRAND_BANNER_JS_TYPE'),
            'logoMinWidth' => Configure::read('Constants.BRAND_LOGO_MIN_WIDTH'),
            'logoMaxWidth' => Configure::read('Constants.BRAND_LOGO_MAX_WIDTH'),
            'logoMinHeight' => Configure::read('Constants.BRAND_LOGO_MIN_HEIGHT'),
            'logoMaxHeight' => Configure::read('Constants.BRAND_LOGO_MAX_HEIGHT'),
            'bannerMinWidth' => Configure::read('Constants.BRAND_BANNER_MIN_WIDTH'),
            'bannerMaxWidth' => Configure::read('Constants.BRAND_BANNER_MAX_WIDTH'),
            'bannerMinHeight' => Configure::read('Constants.BRAND_BANNER_MIN_HEIGHT'),
            'bannerMaxHeight' => Configure::read('Constants.BRAND_BANNER_MAX_HEIGHT'),
            'brandLogoType' => Configure::read('Constants.BRAND_LOGO_TYPE'),
            'brandBannerType' => Configure::read('Constants.BRAND_BANNER_TYPE'),
        ]);

        $brand = $this->Brands->newEmptyEntity();

        if ($this->request->is('post')) {

            $data = $this->request->getData();
            if (isset($data['brand_logo']) && $data['brand_logo']->getError() === UPLOAD_ERR_OK) {
                $brand_logo = $data['brand_logo'];
                $fileName = trim($brand_logo->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $brand_logo->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));

                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.BRAND');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Brand logo could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'add']);
                    } else {
                        $data['brand_logo'] = $folderPath . $imageFile;
                    }
                } else {
                    $this->Flash->error(__('Brand logo is required.'));
                    return $this->redirect(['action' => 'add']);
                }
            } else {
                $data['brand_logo'] = '';
                $this->Flash->error(__('Brand logo is required.'));
                return $this->redirect(['action' => 'add']);
            }
          //  $data['brand_logo'] = 'https://dummyimage.com/320x250/06fc27/000000.png&text=OzoneX+Place+Holder';
            // Handle web_banner upload (optional)
            if (isset($data['web_banner']) && $data['web_banner']->getError() === UPLOAD_ERR_OK) {
                $web_banner = $data['web_banner'];
                $fileName = trim($web_banner->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $web_banner->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.BRAND');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);

                    if ($uploadResult === 'Success') {
                        $data['web_banner'] = $folderPath . $imageFile; // Save filename for DB
                    }
                }
            } else {
                $data['web_banner'] = 'https://dummyimage.com/320x250/06fc27/000000.png&text=OzoneX+Place+Holder';
            }
           
            // $data['web_banner'] = 'https://dummyimage.com/320x250/06fc27/000000.png&text=OzoneX+Place+Holder';
            // Allow users to enter different content for Arabic fields
            if (empty($data['name_ar'])) {
                $data['name_ar'] = $data['name'];
            }
            if (empty($data['description_ar'])) {
                $data['description_ar'] = $data['description'];
            }
            $data['url_key'] = $this->generateUniqueUrlKey($data['name'], 'Brands');

            // Handle country_id assignment based on current country filter
            $selectedCountryId = $this->getCurrentCountryFilter();
            if ($selectedCountryId) {
                // Use the country selected in header dropdown
                $data['country_id'] = $selectedCountryId;
            } else {
                // If "All Countries" is selected, force user to select a specific country first
                $this->Flash->error(__('Please select a specific country from the header dropdown before adding a brand. Brands must be assigned to a specific country.'));

                // Redirect back to add form
                return $this->redirect(['action' => 'add']);
            }

            $brand = $this->Brands->patchEntity($brand, $data);

            if ($this->Brands->save($brand)) {
                $this->saveCategoryMapping($brand);
                $this->Flash->success(__('The brand has been saved.'));
                return $this->redirect(['controller' => 'Brands', 'action' => 'index']);
            } else {
                $this->Flash->error(__('The brand could not be saved. Please, try again.'));
            }
        }

        $categories = $this->Categories->find('treeList', keyPath: 'id', valuePath: 'name')
            ->where(['status' => 'A'])
            ->toArray();

        // Get selected country for display
        $selectedCountryId = $this->request->getSession()->read('Admin.selectedCountryId');
        $selectedCountry = null;
        if ($selectedCountryId) {
            $selectedCountry = $this->Countries->getCountryById($selectedCountryId);
        }

        $title = 'Brand | Add';
        $this->set(compact('brand', 'categories', 'title', 'selectedCountry'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Brand id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {

        $this->set([
            'logoSize' => Configure::read('Constants.BRAND_LOGO_SIZE'),
            'bannerSize' => Configure::read('Constants.BRAND_BANNER_SIZE'),
            'logoType' => Configure::read('Constants.BRAND_LOGO_JS_TYPE'),
            'bannerType' => Configure::read('Constants.BRAND_BANNER_JS_TYPE'),
            'logoMinWidth' => Configure::read('Constants.BRAND_LOGO_MIN_WIDTH'),
            'logoMaxWidth' => Configure::read('Constants.BRAND_LOGO_MAX_WIDTH'),
            'logoMinHeight' => Configure::read('Constants.BRAND_LOGO_MIN_HEIGHT'),
            'logoMaxHeight' => Configure::read('Constants.BRAND_LOGO_MAX_HEIGHT'),
            'bannerMinWidth' => Configure::read('Constants.BRAND_BANNER_MIN_WIDTH'),
            'bannerMaxWidth' => Configure::read('Constants.BRAND_BANNER_MAX_WIDTH'),
            'bannerMinHeight' => Configure::read('Constants.BRAND_BANNER_MIN_HEIGHT'),
            'bannerMaxHeight' => Configure::read('Constants.BRAND_BANNER_MAX_HEIGHT'),
            'brandLogoType' => Configure::read('Constants.BRAND_LOGO_TYPE'),
            'brandBannerType' => Configure::read('Constants.BRAND_BANNER_TYPE'),
        ]);

        $brand = $this->Brands->get($id, contain: ['Categories', 'Countries']);

        // Check if user can access this brand's country
        if (!$this->canUserAccessCountry($brand->country_id)) {
            $this->Flash->error(__('You do not have permission to access this brand.'));
            return $this->redirect(['action' => 'index']);
        }

        $aws_url = Configure::read('Settings.AWS_URL');

        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();

            if (isset($data['brand_logo']) && $data['brand_logo']->getError() === UPLOAD_ERR_OK) {
                $brand_logo = $data['brand_logo'];
                $fileName = trim($brand_logo->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $brand_logo->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.BRAND');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Brand logo could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'edit', $id]);
                    } else {
                        // echo $folderPath . $imageFile; die;
                        $data['brand_logo'] = $folderPath . $imageFile;
                    }
                } else {
                    $this->Flash->error(__('Brand logo is required.'));
                    return $this->redirect(['action' => 'edit', $id]);
                }
            } else {
                $data['brand_logo'] = $this->request->getData('existing_brand_logo');
            }

            if (isset($data['web_banner']) && $data['web_banner']->getError() === UPLOAD_ERR_OK) {
                $web_banner = $data['web_banner'];
                $fileName = trim($web_banner->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $web_banner->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.BRAND');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Web banner could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'edit', $id]);
                    } else {
                        $data['web_banner'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['web_banner'] = $this->request->getData('existing_web_banner');
            }

            $data['url_key'] = $this->generateUniqueUrlKey($data['name'], 'Brands', $id);
            // Allow users to enter different content for Arabic fields
            if (empty($data['name_ar'])) {
                $data['name_ar'] = $data['name'];
            }
            if (empty($data['description_ar'])) {
                $data['description_ar'] = $data['description'];
            }
            $brand = $this->Brands->patchEntity($brand, $data);
            if ($this->Brands->save($brand)) {
                $this->saveCategoryMapping($brand);
                $this->Flash->success(__('The brand has been updated.'));
                return $this->redirect(['controller' => 'Brands', 'action' => 'index']);
            } else {
                $this->Flash->error(__('The brand could not be updated. Please, try again.'));
            }
        }

        $selectedCategories = [];
        if (!empty($brand->categories)) {
            foreach ($brand->categories as $category) {
                $selectedCategories[] = $category->id;
            }
        }

        //$categories = $this->Categories->getCategoryTree();

        $categories = $this->Categories->find('treeList', keyPath: 'id', valuePath: 'name')
            ->where(['status' => 'A'])
            ->toArray();

        $brand_logo = $this->Media->getCloudFrontURL($brand->brand_logo);
        $web_banner = $this->Media->getCloudFrontURL($brand->web_banner);

        $title = 'Brand | Edit';
        $this->set(compact('brand', 'aws_url', 'categories', 'selectedCategories', 'aws_url', 'brand_logo', 'web_banner', 'title'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Brand id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The brand could not be deleted. Please, try again.'];

        try {

            $record = $this->Brands->get($id);

            // Check if user can access this brand's country
            if (!$this->canUserAccessCountry($record->country_id)) {
                $response = ['success' => false, 'message' => 'You do not have permission to delete this brand.'];
            } else {
                if ($record->brand_logo) {
                    $this->Media->awsDelete($record->brand_logo);
                }
                if ($record->web_banner) {
                    $this->Media->awsDelete($record->web_banner);
                }
                if ($record->mobile_banner) {
                    $this->Media->awsDelete($record->mobile_banner);
                }

                $record->status = 'D';
                $record->brand_logo = null;
                $record->web_banner = null;
                $record->mobile_banner = null;

                if ($this->Brands->save($record)) {
                    $response = ['success' => true, 'message' => 'The brand has been marked as deleted.'];
                }
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    protected function saveCategoryMapping($brand)
    {
        if (!empty($brand->category_id) && is_array($brand->category_id)) {

            $this->BrandCategoryMappings->deleteAll(['brand_id' => $brand->id]);

            foreach ($brand->category_id as $categoryId) {
                $mapping = $this->BrandCategoryMappings->find()
                    ->where(['brand_id' => $brand->id, 'category_id' => $categoryId])
                    ->first();

                if ($mapping) {
                    $mapping->category_id = $categoryId;
                } else {
                    $mapping = $this->BrandCategoryMappings->newEntity([
                        'brand_id' => $brand->id,
                        'category_id' => $categoryId
                    ]);
                }

                if (!$this->BrandCategoryMappings->save($mapping)) {
                    return false;
                }
            }
        }

        return true;
    }

    public function filterSearch()
    {
        $status = $this->request->getQuery('filterStatus');
        // $search = $this->request->getQuery('customSearchBox');

        if ($this->request->is('ajax')) {

            $query = $this->Brands->find()
                ->select(['id', 'name', 'name_ar', 'brand_logo', 'status', 'country_id']) // Explicitly select name_ar and country_id
                ->contain([
                    'Categories' => function ($q) {
                        return $q->select(['Categories.id', 'Categories.name']);
                    },
                    'Countries' => function ($q) {
                        return $q->select([
                            'Countries.id',
                            'Countries.name'
                        ]);
                    }
                ]);

            // Apply role-based country filtering
            $query = $this->applyRoleBasedCountryFilter($query, 'Brands.country_id');

            // Apply status filter
            if ($status) {
                $query->where(['Brands.status' => $status]);
            } else {
                $query->where(['Brands.status' => 'A']);
            }

            // Exclude deleted records
            $query->where(['Brands.status !=' => 'D']);

            $brands = [];
            $i = 1;
            foreach ($query as $brand) {

                if (!empty($brand->categories)) {
                    $categoryNames = [];
                    foreach ($brand->categories as $category) {
                        $categoryNames[] = h($category->name);
                    }
                    $category = implode(', ', $categoryNames);
                } else {
                    $category = '-';
                }

                $statusMap = [
                    'A' => ['label' => 'Active', 'class' => 'col-green'],
                    'I' => ['label' => 'Inactive', 'class' => 'col-blue'],
                    'D' => ['label' => 'Deleted', 'class' => 'col-red']
                ];

                $status = $statusMap[$brand->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

                // Get brand logo URL
                $brandLogo = $this->Media->getCloudFrontURL($brand->brand_logo);

                // Get country name
                $countryName = $brand->country ? h($brand->country->name) : '-';

                // Check permissions
                $canView = $this->hasPermission('Brands', 'view');
                $canEdit = $this->hasPermission('Brands', 'edit');
                $canDelete = $this->hasPermission('Brands', 'delete');

                // Build actions based on permissions
                $actions = '';
                if ($canView) {
                    $actions .= '<a href="' . Router::url(['controller' => 'Brands', 'action' => 'view', $brand->id], true) . '" class="btn-view" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a> ';
                }
                if ($canEdit) {
                    $actions .= '<a href="' . Router::url(['controller' => 'Brands', 'action' => 'edit', $brand->id], true) . '" class="btn-edit" data-toggle="tooltip" title="Edit"><i class="far fa-edit m-r-10"></i></a> ';
                }
                if ($canDelete) {
                    $actions .= '<a href="' . Router::url(['controller' => 'Brands', 'action' => 'delete', $brand->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>';
                }

                $brandData = [
                    'id' => $i,
                    'image' => '<img src="' . $brandLogo . '" alt="" />',
                    'name' => h($brand->name),
                    'name_ar' => '<div dir="rtl">' . (!empty($brand->name_ar) ? h($brand->name_ar) : '-') . '</div>',
                    'category' => $category,
                    'country' => $countryName,
                    'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>'
                ];

                // Only add actions column if user has any permissions
                if ($canView || $canEdit || $canDelete) {
                    $brandData['actions'] = $actions;
                }

                $brands[] = $brandData;
                $i++;
            }

            $this->set([
                'brands' => $brands,
                '_serialize' => ['brands'],
            ]);

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['data' => $brands]));
        }

        return null;
    }

    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $imageType = $this->request->getData('image_type');
        $brandId = $this->request->getData('image_id');

        if (!$imageType || !$brandId) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $brand = $this->Brands->get($brandId);

        if (!$brand) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Brand not found']));
        }

        $imageField = $imageType === 'logo' ? 'brand_logo' : 'web_banner';
        $existingImagePath = $brand->{$imageField};

        if ($existingImagePath) {

            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
            $filePath = Configure::read('Settings.BRAND');
            $folderPath = $uploadFolder . $filePath . $existingImagePath;

            $filePath = WWW_ROOT . $uploadFolder . $existingImagePath;
            if (file_exists($folderPath)) {
                unlink($folderPath);
            }

            $brand->{$imageField} = null;
            if ($this->Brands->save($brand)) {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'success', 'message' => 'Image deleted successfully']));
            } else {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update brand']));
            }
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Image not found']));
        }
    }

    public function fetchMappedBrands()
    {
        $this->request->allowMethod(['post', 'ajax']);

        $categoryId = $this->request->getData('category_id');
        $subCategoryId = $this->request->getData('sub_category_id');

        $response = [
            'success' => false,
            'data' => [],
            'message' => __('No brands found.')
        ];

        if ($categoryId || $subCategoryId) {

            $brandMappings = $this->Brands->BrandCategoryMappings->find()
                ->where(['category_id IN' => [$categoryId, $subCategoryId]])
                ->select(['brand_id'])
                ->toArray();

            if (!empty($brandMappings)) {
                $brandIds = array_map(function ($mapping) {
                    return $mapping['brand_id'];
                }, $brandMappings);

                $brands = $this->Brands->find('all')
                    ->where([
                        'id IN' => $brandIds,
                        'status' => 'A'
                    ])
                    ->select(['id', 'name'])
                    ->toArray();

                if (!empty($brands)) {
                    $response['success'] = true;
                    $response['data'] = $brands;
                    $response['message'] = __('Brands found.');
                }
            }
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
    }
}

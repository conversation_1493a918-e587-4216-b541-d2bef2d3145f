<?php

/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Brand> $brands
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<?php $this->end(); ?>
<div class="section-header">
    <ul class="breadcrumb breadcrumb-style ">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Catalogue") ?></li>
        <li class="breadcrumb-item active"><?= __("Brands") ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("Brands") ?></h4>
                <div class="card-header-form">
                    <div class="input-group">
                        <input type="text" class="form-control search-control" placeholder="<?= __("Search") ?>"
                            id="customSearchBox" />
                        <div class="input-group-btn">
                            <button class="btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <?php if ($canAdd): ?>
                            <a href="<?= $this->Url->build(['controller' => 'Brands', 'action' => 'add']) ?>"
                                class="btn m-r-15">
                                <i class="fas fa-plus"></i>
                                <?= __("Add Brand") ?>
                            </a>
                        <?php endif; ?>
                        <button class="btn menu-toggle" type="submit">
                            <i class="fas fa-filter"></i>
                            <?= __("Filter") ?>
                        </button>
                    </div>
                </div>
            </div>
            <div id="filter-body-container">
                <div class="input-group m-l-25">
                    <div class="d-flex align-items-center">
                        <div class="form-group d-flex align-items-center m-l-20">
                            <?php echo $this->Form->control('status', [
                                'type' => 'select',
                                'options' => $status,
                                'id' => 'filterStatus',
                                'class' => 'form-control form-select',
                                'label' => false,
                                'empty' => __('Filter By Status'),
                                'data' => ['bs-toggle' => 'dropdown'],
                                'aria-expanded' => 'false'
                            ]) ?>
                        </div>
                        <div class="form-group ms-4">
                            <button class="btn btn-primary p-10" id="filter">
                                <i class="fa fa-filter" aria-hidden="true"></i>
                            </button>
                            <button type="reset" class="btn btn-primary p-10" onclick="resetFilters()"><i
                                    class="fas fa-redo-alt"></i></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="table-1">
                        <thead>
                            <tr>
                                <th><?= __("Id") ?></th>
                                <th><?= __('Brand Image') ?></th>
                                <th><?= __("Brand Name") ?></th>
                                <th><?= __("Brand Name (Arabic)") ?></th>
                                <th><?= __("Category") ?></th>
                                <th><?= __("Country") ?></th>
                                <th id="status"><?= __("Status") ?></th>
                                <?php if ($canView || $canEdit || $canDelete): ?>
                                    <th class="actions"><?= __("Actions") ?></th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1;
                            foreach ($brands as $list): ?>
                                <tr>
                                    <td><?= $i ?></td>
                                    <td class="table-img">
                                        <img src="<?= $list->brand_logo ?>" alt="" />
                                    </td>
                                    <td><?= h($list->name) ?></td>
                                    <td dir="rtl"><?= !empty($list->name_ar) ? h($list->name_ar) : '-' ?></td>
                                    <td>
                                        <?php
                                        if (!empty($list->categories)) {
                                            $categoryNames = [];
                                            foreach ($list->categories as $category) {
                                                $categoryNames[] = h($category->name);
                                            }
                                            echo implode(', ', $categoryNames);
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($list->country)): ?>
                                            <span class="badge badge-success">
                                                <i class="fas fa-map-marker-alt me-1"></i><?= h($list->country->name) ?>
                                            </span>
                                        <?php else: ?>
                                            <?php print_r($list); ?>
                                            <span class="badge badge-secondary">
                                                <i class="fas fa-globe me-1"></i><?= __('Global') ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>

                                    <td>
                                        <?php
                                        $statusval = $statusMap[$list->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                        ?>
                                        <div class="badge-outline <?= $statusval['class'] ?>">
                                            <?= h($statusval['label']) ?>
                                        </div>
                                    </td>
                                    <?php if ($canView || $canEdit || $canDelete): ?>
                                        <td class="actions">
                                            <?php if ($canView): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Brands', 'action' => 'view', $list->id]) ?>"
                                                    class="" data-toggle="tooltip" title="View"><i
                                                        class="far fa-eye m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canEdit): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Brands', 'action' => 'edit', $list->id]) ?>"
                                                    class="" data-toggle="tooltip" title="Edit"><i
                                                        class="fas fa-pencil-alt m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canDelete): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Brands', 'action' => 'delete', $list->id]) ?>"
                                                    class="delete-btn" data-toggle="tooltip" title="Delete"
                                                    data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                                                    data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                                                    data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                                                    <i class="far fa-trash-alt"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    <?php endif; ?>
                                </tr>
                            <?php $i++;
                            endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script
    src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script>
    var paginationCount = <?= json_encode($paginationCount) ?>;
    var hasActionsColumn = <?= ($canView || $canEdit || $canDelete) ? 'true' : 'false' ?>;

    var columnDefs = [
        {
            // Make Arabic name column right-aligned
            className: "text-right",
            targets: 3
        }
    ];

    // Only add orderable: false for actions column if it exists
    if (hasActionsColumn) {
        columnDefs.push({
            orderable: false,
            targets: -1
        });
    }

    var table = $("#table-1").DataTable({
        columnDefs: columnDefs,
        dom: 'rtip',
        pageLength: paginationCount,
        drawCallback: function() {
            var api = this.api();
            api.column(0, {
                search: 'applied',
                order: 'applied'
            }).nodes().each(function(cell, i) {
                cell.innerHTML = i + 1;
            });
        }
    });

    // table.column(3).search('Active', true, false, false).draw();

    $('#customSearchBox').on('keyup', function() {
        table.search(this.value).draw();
    });

    function resetFilters() {
        $('#customSearchBox').val('');
        $('#filterStatus').val('');
        table.search('').columns().search('').draw();
        // Reload data with default filters
        performSearch();
    }

    $('#filter').on('click', function(event) {
        event.preventDefault();
        performSearch();
    });

    function performSearch() {
        var filterStatus = $('#filterStatus').val();

        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Brands', 'action' => 'filterSearch']) ?>',
            type: 'GET',
            data: {
                filterStatus: filterStatus
            },
            success: function(response) {
                console.log('AJAX Response:', response);
                console.log('Data structure:', response.data);
                if (response.data && response.data.length > 0) {
                    console.log('First row:', response.data[0]);
                    console.log('Number of columns in first row:', Object.keys(response.data[0]).length);
                }
                table.clear().rows.add(response.data).draw();
            },
            error: function(xhr, status, error) {
                console.log('Error:', error);
            }
        });
    }
</script>
<?php $this->end(); ?>
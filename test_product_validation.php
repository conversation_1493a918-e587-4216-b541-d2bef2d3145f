<?php
/**
 * Test script to validate product data and identify validation issues
 * Place this file in the webroot and access it via browser
 */

// Include CakePHP bootstrap
require_once dirname(__DIR__) . '/vendor/autoload.php';

use Cake\Core\Configure;
use Cake\Core\Configure\Engine\PhpConfig;
use Cake\Datasource\ConnectionManager;
use Cake\ORM\TableRegistry;

try {
    // Load configuration
    Configure::config('default', new PhpConfig());
    Configure::load('app', 'default', false);
    
    // Load local config if exists
    if (file_exists(dirname(__DIR__) . '/config/app_local.php')) {
        Configure::load('app_local', 'default');
    }
    
    echo "<h1>Product Validation Test</h1>";
    
    // Test database connection
    try {
        $connection = ConnectionManager::get('default');
        $connection->connect();
        echo "<p style='color: green;'>✓ Database connection successful</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
        exit;
    }
    
    // Get Products table
    $productsTable = TableRegistry::getTableLocator()->get('Products');
    
    echo "<h2>Testing Product Validation Rules</h2>";
    
    // Test 1: Create a minimal valid product
    echo "<h3>Test 1: Minimal Valid Product</h3>";
    $testProduct1 = $productsTable->newEmptyEntity();
    $testData1 = [
        'name' => 'Test Product',
        'reference_name' => 'TEST001',
        'description' => 'Test description',
        'product_size' => 'Medium',
        'sku' => 'TEST_' . time(),
        'brand_id' => 1, // Assuming brand ID 1 exists
        'status' => 'A'
    ];
    
    $testProduct1 = $productsTable->patchEntity($testProduct1, $testData1);
    
    if ($testProduct1->getErrors()) {
        echo "<p style='color: red;'>✗ Validation failed:</p>";
        echo "<pre>" . print_r($testProduct1->getErrors(), true) . "</pre>";
    } else {
        echo "<p style='color: green;'>✓ Validation passed</p>";
    }
    
    // Test 2: Product with catalogue field
    echo "<h3>Test 2: Product with Catalogue Field</h3>";
    $testProduct2 = $productsTable->newEmptyEntity();
    $testData2 = array_merge($testData1, [
        'sku' => 'TEST_CAT_' . time(),
        'catalogue' => 'test_catalogue.pdf'
    ]);
    
    $testProduct2 = $productsTable->patchEntity($testProduct2, $testData2);
    
    if ($testProduct2->getErrors()) {
        echo "<p style='color: red;'>✗ Validation failed:</p>";
        echo "<pre>" . print_r($testProduct2->getErrors(), true) . "</pre>";
    } else {
        echo "<p style='color: green;'>✓ Validation passed</p>";
    }
    
    // Test 3: Product with price fields
    echo "<h3>Test 3: Product with Price Fields</h3>";
    $testProduct3 = $productsTable->newEmptyEntity();
    $testData3 = array_merge($testData1, [
        'sku' => 'TEST_PRICE_' . time(),
        'sales_price' => 100.00,
        'promotion_price' => 80.00
    ]);
    
    $testProduct3 = $productsTable->patchEntity($testProduct3, $testData3);
    
    if ($testProduct3->getErrors()) {
        echo "<p style='color: red;'>✗ Validation failed:</p>";
        echo "<pre>" . print_r($testProduct3->getErrors(), true) . "</pre>";
    } else {
        echo "<p style='color: green;'>✓ Validation passed</p>";
    }
    
    // Test 4: Product with invalid price relationship
    echo "<h3>Test 4: Product with Invalid Price Relationship</h3>";
    $testProduct4 = $productsTable->newEmptyEntity();
    $testData4 = array_merge($testData1, [
        'sku' => 'TEST_INVALID_' . time(),
        'sales_price' => 80.00,
        'promotion_price' => 100.00 // Higher than sales price
    ]);
    
    $testProduct4 = $productsTable->patchEntity($testProduct4, $testData4);
    
    if ($testProduct4->getErrors()) {
        echo "<p style='color: red;'>✗ Validation failed (expected):</p>";
        echo "<pre>" . print_r($testProduct4->getErrors(), true) . "</pre>";
    } else {
        echo "<p style='color: orange;'>⚠ Validation passed (unexpected - should have failed)</p>";
    }
    
    // Test 5: Check existing brands
    echo "<h3>Test 5: Available Brands</h3>";
    $brandsTable = TableRegistry::getTableLocator()->get('Brands');
    $brands = $brandsTable->find()->where(['status' => 'A'])->limit(5)->toArray();
    
    if (empty($brands)) {
        echo "<p style='color: red;'>✗ No active brands found</p>";
    } else {
        echo "<p style='color: green;'>✓ Found " . count($brands) . " active brands:</p>";
        echo "<ul>";
        foreach ($brands as $brand) {
            echo "<li>ID: {$brand->id}, Name: {$brand->name}</li>";
        }
        echo "</ul>";
    }
    
    // Test 6: Check validation rules
    echo "<h3>Test 6: Validation Rules Analysis</h3>";
    $validator = $productsTable->getValidator('default');
    echo "<p>Validation rules are configured for the Products table.</p>";
    
    echo "<h2>Recommendations</h2>";
    echo "<ul>";
    echo "<li>Enable debug mode in config/app_local.php</li>";
    echo "<li>Check logs/debug.log and logs/error.log for detailed error messages</li>";
    echo "<li>Ensure all required fields are provided when editing products</li>";
    echo "<li>Verify that brand_id exists and is valid</li>";
    echo "<li>Check that catalogue files are valid PDF files under 10MB</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h1>Error</h1>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

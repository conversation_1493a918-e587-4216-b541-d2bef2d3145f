<div class="navbar-bg"></div>
<nav class="navbar navbar-expand-lg main-navbar sticky">
    <div class="form-inline me-auto">
        <ul class="navbar-nav mr-3">
            <li><a href="#" data-bs-toggle="sidebar" class="nav-link nav-link-lg collapse-btn"> <i data-feather="menu"></i></a></li>
        </ul>
    </div>
    <ul class="navbar-nav navbar-right d-flex align-items-center">


        <!-- Country Filter Dropdown -->
        <li class="dropdown me-3">
            <a href="#" data-bs-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg" id="countryFilterDropdown">
                <i class="fas fa-globe me-1"></i>
                <span id="selectedCountryText">
                    <?php
                    // Safe country name display
                    if (isset($selectedCountry) && $selectedCountry) {
                        if (is_object($selectedCountry) && isset($selectedCountry->name)) {
                            echo h($selectedCountry->name);
                        } elseif (is_array($selectedCountry) && isset($selectedCountry['name'])) {
                            echo h($selectedCountry['name']);
                        } elseif (isset($selectedCountryId) && isset($countries[$selectedCountryId])) {
                            echo h($countries[$selectedCountryId]);
                        } else {
                            echo __('All Countries');
                        }
                    } else {
                        echo __('All Countries');
                    }
                    ?>
                </span>
            </a>
            <div class="dropdown-menu dropdown-menu-right pullDown" style="min-width: 200px;">
                <div class="dropdown-title"><?= __('Filter by Country') ?></div>
                <a href="#" class="dropdown-item country-filter-item <?= (!$selectedCountryId) ? 'active' : '' ?>" data-country-id="">
                    <i class="fas fa-globe me-2"></i><?= __('All Countries') ?>
                </a>
                <div class="dropdown-divider"></div>
                <?php if (isset($countries) && !empty($countries)): ?>
                    <?php foreach ($countries as $countryId => $countryName): ?>
                        <a href="#" class="dropdown-item country-filter-item <?= ($selectedCountryId == $countryId) ? 'active' : '' ?>"
                           data-country-id="<?= $countryId ?>">
                            <?php
                                if (is_object($countryName) && isset($countryName->name)) {
                                    echo '<i class="fas fa-map-marker-alt me-2"></i>' . h($countryName->name);
                                } else {
                                    echo '<i class="fas fa-map-marker-alt me-2"></i>' . h($countryName);
                                }
                            ?>
                        </a>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </li>
        <li><a href="#" class="nav-link nav-link-lg fullscreen-btn">
                <i data-feather="maximize"></i>
            </a></li>
        <li class="dropdown"><a href="#" data-bs-toggle="dropdown"
                class="nav-link dropdown-toggle nav-link-lg nav-link-user"> <img alt="image" src="<?= $this->Url->webroot('img/user.png') ?>"
                    class="user-img-radious-style"> <span class="d-sm-none d-lg-inline-block"></span></a>
            <div class="dropdown-menu dropdown-menu-right pullDown">
                <?php if (isset($user)): ?><div class="dropdown-title">Hello <?= h($user->first_name . ' ' . $user->last_name) ?></div><?php endif; ?>
                <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'editProfile', $user->id]) ?>" class="dropdown-item has-icon"> <i class="far
                                    fa-user"></i> Profile
                </a><a href="<?= $this->Url->build(['controller' => 'SiteSettings', 'action' => 'index']) ?>" class="dropdown-item has-icon"> <i class="fas fa-cog"></i>
                    Settings
                </a>
                <div class="dropdown-divider"></div>
                <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'logout']) ?>" class="dropdown-item has-icon text-danger"> <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </li>
    </ul>
</nav>
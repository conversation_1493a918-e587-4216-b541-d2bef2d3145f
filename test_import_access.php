<?php
/**
 * Simple test to check if import URLs are accessible
 */

echo "<h1>Product Import URL Test</h1>";

$baseUrl = "http://localhost/carmatec/Ozone/cakephpapp";

$urls = [
    'Products Index' => $baseUrl . '/products',
    'Import Products' => $baseUrl . '/products/importProducts',
    'Process Import' => $baseUrl . '/products/processImport',
    'Download Sample CSV' => $baseUrl . '/products/downloadSampleCsv',
    'Download Sample Excel' => $baseUrl . '/products/downloadSampleExcel'
];

echo "<h2>Testing URL Accessibility</h2>";

foreach ($urls as $name => $url) {
    echo "<h3>Testing: {$name}</h3>";
    echo "URL: <a href='{$url}' target='_blank'>{$url}</a><br>";
    
    // Test if URL is reachable
    $headers = @get_headers($url);
    if ($headers) {
        $status = $headers[0];
        if (strpos($status, '200') !== false) {
            echo "✓ Status: <span style='color: green;'>{$status}</span><br>";
        } elseif (strpos($status, '302') !== false || strpos($status, '301') !== false) {
            echo "⚠ Status: <span style='color: orange;'>{$status}</span> (Redirect - may require login)<br>";
        } else {
            echo "✗ Status: <span style='color: red;'>{$status}</span><br>";
        }
    } else {
        echo "✗ Status: <span style='color: red;'>Unable to connect</span><br>";
    }
    echo "<br>";
}

echo "<h2>JavaScript Test</h2>";
echo "<p>Click the button below to test the import dropdown functionality:</p>";
?>

<button onclick="testImportDropdown()" class="btn btn-primary">Test Import Dropdown</button>
<div id="test-result"></div>

<script>
function testImportDropdown() {
    const resultDiv = document.getElementById('test-result');
    
    // Test if jQuery is available
    if (typeof jQuery === 'undefined') {
        resultDiv.innerHTML = '<p style="color: red;">❌ jQuery is not loaded</p>';
        return;
    }
    
    resultDiv.innerHTML = '<p style="color: green;">✅ jQuery is available</p>';
    
    // Test if Bootstrap dropdown functionality works
    if (typeof jQuery.fn.dropdown === 'undefined') {
        resultDiv.innerHTML += '<p style="color: red;">❌ Bootstrap dropdown is not available</p>';
        return;
    }
    
    resultDiv.innerHTML += '<p style="color: green;">✅ Bootstrap dropdown is available</p>';
    
    // Test the import URLs
    const importUrl = '<?= $baseUrl ?>/products/importProducts';
    
    fetch(importUrl, {
        method: 'HEAD',
        mode: 'no-cors'
    })
    .then(() => {
        resultDiv.innerHTML += '<p style="color: green;">✅ Import URL is accessible</p>';
    })
    .catch(error => {
        resultDiv.innerHTML += '<p style="color: red;">❌ Import URL test failed: ' + error.message + '</p>';
    });
}
</script>

<style>
.btn {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin: 10px 0;
}

.btn:hover {
    background-color: #0056b3;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>

<?php
echo "<h2>Manual Testing Instructions</h2>";
echo "<ol>";
echo "<li>Open the Products page: <a href='{$baseUrl}/products' target='_blank'>Products Index</a></li>";
echo "<li>Look for the 'Import Products' dropdown button</li>";
echo "<li>Click on it and select 'New Import (CSV/Excel)'</li>";
echo "<li>You should be redirected to the import page</li>";
echo "<li>Test downloading sample files</li>";
echo "<li>Test uploading a sample CSV file</li>";
echo "</ol>";

echo "<h2>Troubleshooting</h2>";
echo "<ul>";
echo "<li>If you get 'mData' errors, check browser console for JavaScript errors</li>";
echo "<li>If import button doesn't work, check if user has proper permissions</li>";
echo "<li>If pages don't load, check if routes are configured correctly</li>";
echo "<li>If file upload fails, check file permissions and size limits</li>";
echo "</ul>";
?>

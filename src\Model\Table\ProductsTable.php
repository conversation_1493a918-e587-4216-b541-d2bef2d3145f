<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Database\Expression\QueryExpression;
use Cake\ORM\Locator\TableLocator;
use Cake\I18n\FrozenTime;
use Cake\Datasource\EntityInterface;
use Cake\Http\ServerRequestFactory;
/**
 * Products Model
 *
 * @property \App\Model\Table\BrandsTable&\Cake\ORM\Association\BelongsTo $Brands
 * @property \App\Model\Table\SuppliersTable&\Cake\ORM\Association\BelongsTo $Suppliers
 * @property \App\Model\Table\CategoriesTable&\Cake\ORM\Association\BelongsToMany $Categories
 * @property \App\Model\Table\BannerAdsTable&\Cake\ORM\Association\HasMany $BannerAds
 * @property \App\Model\Table\CartItemsTable&\Cake\ORM\Association\HasMany $CartItems
 * @property \App\Model\Table\InventoriesTable&\Cake\ORM\Association\HasMany $Inventories
 * @property \App\Model\Table\OrderItemsTable&\Cake\ORM\Association\HasMany $OrderItems
 * @property \App\Model\Table\ProductAttributesTable&\Cake\ORM\Association\HasMany $ProductAttributes
 * @property \App\Model\Table\ProductCategoriesTable&\Cake\ORM\Association\HasMany $ProductCategories
 * @property \App\Model\Table\ProductImagesTable&\Cake\ORM\Association\HasMany $ProductImages
 * @property \App\Model\Table\ProductVariantsTable&\Cake\ORM\Association\HasMany $ProductVariants
 * @property \App\Model\Table\ReturnItemsTable&\Cake\ORM\Association\HasMany $ReturnItems
 * @property \App\Model\Table\ReviewsTable&\Cake\ORM\Association\HasMany $Reviews
 * @property \App\Model\Table\ShowroomStocksTable&\Cake\ORM\Association\HasMany $ShowroomStocks
 * @property \App\Model\Table\SupplierPurchaseOrderItemsTable&\Cake\ORM\Association\HasMany $SupplierPurchaseOrderItems
 * @property \App\Model\Table\SupplierStocksTable&\Cake\ORM\Association\HasMany $SupplierStocks
 * @property \App\Model\Table\WarehouseStocksTable&\Cake\ORM\Association\HasMany $WarehouseStocks
 * @property \App\Model\Table\WishlistsTable&\Cake\ORM\Association\HasMany $Wishlists
 *
 * @method \App\Model\Entity\Product newEmptyEntity()
 * @method \App\Model\Entity\Product newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Product> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Product get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Product findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Product patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Product> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Product|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Product saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Product>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Product>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Product>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Product> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Product>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Product>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Product>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Product> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class ProductsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    protected $language;
    protected $country;
    protected $country_id;

    public function initialize(array $config): void
    {
        $request = ServerRequestFactory::fromGlobals();
        $this->language = $request->getSession()->read('siteSettings.language') ?? 'English';
        $this->country = $request->getSession()->read('siteSettings.country') ?? 'Qatar';
        $this->country_id = $request->getSession()->read('siteSettings.country_id') ?? 1;

        parent::initialize($config);

        $this->setTable('products');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Brands', [
            'foreignKey' => 'brand_id',
            'joinType' => 'INNER',
        ]);

        $this->belongsTo('Countries', [
            'foreignKey' => 'country_id',
            'joinType' => 'LEFT', // LEFT JOIN since country_id can be NULL for global products
        ]);

        $this->hasMany('SupplierProducts', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('BannerAds', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('CartItems', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('Inventories', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('OrderItems', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('ProductAttributes', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('ProductCategories', [
            'foreignKey' => 'product_id',
        ]);

        $this->hasMany('ProductImages', [
            'foreignKey' => 'product_id',
        ]);

        $this->hasMany('ProductVariants', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('ReturnItems', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('Reviews', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('ProductStocks', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('PurchaseOrderProducts', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('StockRequestItems', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('SupplierStocks', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('WarehouseStocks', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('Wishlists', [
            'foreignKey' => 'product_id',
        ]);

        $this->hasMany('ProductShowroomPrices', [
            'foreignKey' => 'product_id',
        ]);

        $this->hasMany('RelatedProducts', [
            'className' => 'RelatedProducts', // Reference to the RelatedProducts table
            'foreignKey' => 'product_id', // This matches the 'product_id' in related_products table
            'joinType' => 'INNER',
        ]);

        // Optionally, if you want to retrieve the related products through the related_id
        $this->hasMany('RelatedProductsForThisProduct', [
            'className' => 'RelatedProducts', // Reference to the RelatedProducts table
            'foreignKey' => 'related_id', // This allows you to get products related to this product
            'joinType' => 'INNER',
        ]);

        $this->hasMany('ProductDeals', [
            'foreignKey' => 'product_id',
        ]);

        // Many-to-many association with Categories through ProductCategories junction table
        $this->belongsToMany('Categories', [
            'foreignKey' => 'product_id',
            'targetForeignKey' => 'category_id',
            'joinTable' => 'product_categories',
            'through' => 'ProductCategories'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('brand_id')
            ->notEmptyString('brand_id');

        $validator
            ->nonNegativeInteger('supplier_id')
            ->allowEmptyString('supplier_id');

        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');

        $validator
            ->scalar('reference_name')
            ->maxLength('reference_name', 255)
            ->requirePresence('reference_name', 'create')
            ->notEmptyString('reference_name');

        $validator
            ->scalar('description')
            ->requirePresence('description', 'create')
            ->notEmptyString('description');

        $validator
            ->scalar('details')
            ->allowEmptyString('details');

        $validator
            ->scalar('features')
            ->allowEmptyString('features');

        $validator
            ->scalar('features_ar')
            ->allowEmptyString('features_ar');

        $validator
            ->scalar('product_preference')
            ->allowEmptyString('product_preference');

        $validator
            ->scalar('product_size')
            ->requirePresence('product_size', 'create')
            ->notEmptyString('product_size');

        $validator
            ->decimal('product_weight')
            ->allowEmptyString('product_weight');

        $validator
            ->scalar('product_model')
            ->maxLength('product_model', 255)
            ->allowEmptyString('product_model');

        $validator
            ->scalar('product_tags')
            ->maxLength('product_tags', 255)
            ->allowEmptyString('product_tags');

        $validator
            ->scalar('sku')
            ->allowEmptyString('sku');

            $validator
            ->requirePresence('sku', 'create')
            ->notEmptyString('sku', 'Please enter the SKU ID.')
            ->maxLength('sku', 100, 'SKU ID cannot exceed 100 characters.')
            ->add('sku', 'uniqueActiveSKU', [
                'rule' => function ($value, $context) {
                    // if (!isset($context['data']['status']) || $context['data']['status'] === 'D') {
                    //     return true; // Skip validation if status is 'D'
                    // }

                    $productsTable = \Cake\ORM\TableRegistry::getTableLocator()->get('Products');
                    $query = $productsTable->find()
                        ->where(['sku' => $value, 'status !=' => 'D']);

                    if (!empty($context['data']['id'])) {
                        // Exclude the current record in case of update
                        $query->where(['id !=' => $context['data']['id']]);
                    }

                    return !$query->count();
                },
                'message' => 'This SKU ID is already in use. Please enter a unique SKU ID.'
            ]);

        return $validator;


        $validator
            ->scalar('barcode')
            ->maxLength('barcode', 255)
            ->allowEmptyString('barcode');

        $validator
            ->scalar('qrcode')
            ->maxLength('qrcode', 255)
            ->allowEmptyString('qrcode');

        $validator
            ->scalar('scanned_barcode')
            ->maxLength('scanned_barcode', 255)
            ->allowEmptyString('scanned_barcode');

        // $validator
        //     ->integer('quantity')
        //     ->requirePresence('quantity', 'create')
        //     ->notEmptyString('quantity');

        $validator
            ->decimal('purchase_price')
            ->allowEmptyString('purchase_price');

        $validator
            ->decimal('product_price')
            ->allowEmptyString('product_price');

        $validator
            ->decimal('sales_price')
            ->allowEmptyString('sales_price');

        $validator
            ->decimal('promotion_price')
            ->allowEmptyString('promotion_price');

        $validator
            ->date('promotion_start_date')
            ->allowEmptyDate('promotion_start_date');

        $validator
            ->date('promotion_end_date')
            ->allowEmptyDate('promotion_end_date');

        $validator
            ->integer('max_buy_limit')
            ->allowEmptyString('max_buy_limit');


        $validator
            ->boolean('return_allow')
            ->allowEmptyString('return_allow');

        $validator
            ->integer('return_time_period')
            ->allowEmptyString('return_time_period');

        $validator
            ->scalar('meta_title')
            ->maxLength('meta_title', 255)
            ->allowEmptyString('meta_title');

        $validator
            ->scalar('meta_keyword')
            ->maxLength('meta_keyword', 255)
            ->allowEmptyString('meta_keyword');

        $validator
            ->scalar('meta_description')
            ->allowEmptyString('meta_description');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['brand_id'], 'Brands'), ['errorField' => 'brand_id']);

        return $rules;
    }






    /*****  Product List Start *****/


    //ax
    public function optimizedProductList($params)
    {
        $query = $this->find('all');

        // Apply filters
        $query = $this->applyFilters($query, $params);

        // Apply sorting
        $query = $this->applySorting($query, $params['sort'] ?? 'relevance');

        // Select fields and join tables
        $query = $this->selectFieldsAndJoinTables($query);

        // Get total count before applying pagination
        $totalResults = $query->count();

        // Get available brands for the filtered products
        $availableBrands = $this->getAvailableBrands($params);

        // Apply pagination
        $page = (int)($params['page'] ?? 1);
        $limit = (int)($params['limit'] ?? 20);
        $query->page($page, $limit);

        // Execute query and get results
        $result = $query->toArray();

        // Calculate next and previous page
        $totalPages = (int)ceil($totalResults / $limit);
        $nextPage = $page < $totalPages ? $page + 1 : null;
        $previousPage = $page > 1 ? $page - 1 : null;

        // Get filtered count after applying filters
        $filteredCount = count($result);

        return [
            'current_page' => $page,
            'next_page' => $nextPage,
            'previous_page' => $PreviousPage ?? null,
            'total_count' => $totalResults,
            'filtered_count' => $filteredCount,
            'data' => $result ?: null,
          //  'available_brands' => $availableBrands
        ];
    }

    private function applyFilters($query, $params)
    {
        $conditions = [
            'Products.status' => 'A',
            'Products.country_id' => $this->country_id,
            'Products.approval_status' => 'Approved',
        ];

        if (!empty($params['category'])) {
            $conditions['ProductCategories.category_id IN'] = $params['category'];
        }

        if (!empty($params['filtercategory'])) {
            $filtercategory_arr = explode("--", trim($params['filtercategory']));
            $conditions['ProductCategories2.category_id IN'] = $filtercategory_arr;
        }

        if (!empty($params['brand'])) {
            $brand_arr = explode("--", trim($params['brand']));
            $conditions['Products.brand_id IN'] = $brand_arr;
        }

        if (!empty($params['price'])) {
            $priceRange = explode("--", trim($params['price']));
            $conditions['Products.promotion_price >='] = $priceRange[0];
            $conditions['Products.promotion_price <='] = $priceRange[1];
        }

        // Handle attribute filters
        if (!empty($params['attribute_filter'])) {
            // If attribute_filter is a string, convert it to an array
            if (is_string($params['attribute_filter'])) {
                $attributeFilters = explode(',', $params['attribute_filter']);

                // Group attribute filters by attribute_id
                $attributeGroups = [];
                foreach ($attributeFilters as $attributeFilter) {
                    // Format is expected to be attribute_id-attribute_value_id
                    $parts = explode('-', $attributeFilter);
                    if (count($parts) === 2) {
                        $attributeId = $parts[0];
                        $attributeValueId = $parts[1];

                        // Group attribute values by attribute ID
                        if (!isset($attributeGroups[$attributeId])) {
                            $attributeGroups[$attributeId] = [];
                        }
                        $attributeGroups[$attributeId][] = $attributeValueId;
                    }
                }

                // Add a matching condition for each attribute group
                foreach ($attributeGroups as $attributeId => $attributeValueIds) {
                    $query->innerJoin(
                        ['ProductAttributes' . $attributeId => 'product_attributes'],
                        [
                            'ProductAttributes' . $attributeId . '.product_id = Products.id',
                            'ProductAttributes' . $attributeId . '.attribute_id' => $attributeId,
                            'ProductAttributes' . $attributeId . '.attribute_value_id IN' => $attributeValueIds,
                            'ProductAttributes' . $attributeId . '.status' => 'A'
                        ]
                    );
                }
            }
        }

        if (!empty($params['price_discount'])) {
            $conditions[] = $query->newExpr()->gte(
                $query->newExpr()->add('((Products.sales_price - Products.promotion_price)*100/Products.sales_price)'),
                trim($params['price_discount'])
            );
        }

        return $query->where($conditions);
    }

    private function applySorting($query, $sort)
    {
        $sortMap = [
            'low-to-high' => ['Products.promotion_price' => 'ASC'],
            'high-to-low' => ['Products.promotion_price' => 'DESC'],
            'new-arrival' => ['Products.created' => 'DESC'],
            'relevance' => ['Reviews.rating' => 'DESC'],
            'discount' => ['((Products.sales_price - Products.promotion_price)*100/Products.sales_price)' => 'DESC'],
            'popularity' => ['Reviews.rating' => 'DESC'],
        ];

        $order = $sortMap[$sort] ?? ['Reviews.rating' => 'DESC'];
        return $query->order($order);
    }

    private function selectFieldsAndJoinTables($query)
    {
        $caseExpressions = $this->getCaseExpressions();

        return $query->select([
            'Products.id',
            'Products.name',
            'Products.description',
            'Products.url_key',
            'Brands.name',
            'thumb_image' => '(COALESCE(
            (SELECT pi.image FROM product_images pi 
             WHERE pi.product_id = Products.id 
             AND pi.status = "A" 
             AND pi.image_default = 1 
             LIMIT 1),
            (SELECT pi.image FROM product_images pi 
             WHERE pi.product_id = Products.id 
             AND pi.status = "A" 
             LIMIT 1)
        ))',
            'discount' => $this->find()->newExpr()->add('ROUND((Products.sales_price - Products.promotion_price) * 100 / Products.sales_price, 2)'),
            'Reviews.rating',
            'sales_price' => $caseExpressions['sales_price'],
            'promotion_price' => $caseExpressions['promotion_price'],
            'promotion_start_date' => $caseExpressions['start_date'],
            'promotion_end_date' => $caseExpressions['end_date'],
            'IsDealActive' => $caseExpressions['is_deal_active'],
            'avg_rating' => $query->newExpr()->add('ROUND(AVG(Reviews.rating), 1)'),
        ])
        ->contain([
            'ProductImages' => function ($q) {
                return $q->where(['ProductImages.status' => 'A'])
                        ->order(['ProductImages.image_default' => 'DESC'])
                        ->limit(1);
            },
           // 'ProductCategories' => ['Categories'],
           // 'ProductDeals'
           'ProductAttributes' => [
               'Attributes',
               'AttributeValues'
           ],
            'Reviews' => [
                'fields' => ['id', 'product_id', 'rating', 'comment'],
                'sort' => ['created' => 'DESC']
            ]
        ])
        ->join([
            'Brands' => [
                'table' => 'brands',
                'type' => 'INNER',
                'conditions' => 'Products.brand_id = Brands.id'
            ],
            'ProductCategories' => [
                'table' => 'product_categories',
                'type' => 'LEFT',
                'conditions' => 'ProductCategories.product_id = Products.id'
            ],
            'ProductCategories2' => [
                'table' => 'product_categories',
                'type' => 'LEFT',
                'conditions' => ['ProductCategories2.product_id = Products.id', 'ProductCategories2.level IN' => [1, 2]]
            ],
            'Reviews' => [
                'table' => 'reviews',
                'type' => 'LEFT',
                'conditions' => 'Reviews.product_id = Products.id'
            ],
            // 'ProductDeals' => [
            //     'table' => 'product_deals',
            //     'type' => 'LEFT',
            //     'conditions' => [
            //         'ProductDeals.product_id = Products.id',
            //         'ProductDeals.status = "A"',
            //         'CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date'
            //     ]
            // ]
        ])
        ->group(['Products.id']);
    }

    private function getCaseExpressions()
    {
        $query = $this->find();
        return [
            'promotion_price' => $query->newExpr()->add('Products.promotion_price'),
            'sales_price' => $query->newExpr()->add('Products.sales_price'),
            'start_date' => $query->newExpr()->add('Products.promotion_start_date'),
            'end_date' => $query->newExpr()->add('Products.promotion_end_date'),
            'is_deal_active' => $query->newExpr()->add('
                CASE
                    WHEN CURRENT_DATE BETWEEN Products.promotion_start_date AND Products.promotion_end_date
                    THEN 1
                    ELSE 0
                END
            '),
        ];
    }

    /**
     * Get available brands based on the current filter parameters
     *
     * @param array $params Filter parameters
     * @return array List of available brands
     */
    private function getAvailableBrands($params)
    {
        // Create a new query to get brands
        $brandsQuery = $this->find('all');

        // Apply the same filters as the product query, except for brand filter
        $conditions = [
            'Products.status' => 'A',
        ];

        // Add category filter if present
        if (!empty($params['category'])) {
            $conditions['ProductCategories.category_id IN'] = $params['category'];
        }

        // Add subcategory filter if present
        if (!empty($params['filtercategory'])) {
            $filtercategory_arr = explode("--", trim($params['filtercategory']));
            $conditions['ProductCategories2.category_id IN'] = $filtercategory_arr;
        }

        // Add price range filter if present
        if (!empty($params['price'])) {
            $priceRange = explode("--", trim($params['price']));
            $conditions['Products.promotion_price >='] = $priceRange[0];
            $conditions['Products.promotion_price <='] = $priceRange[1];
        }

        // Add discount filter if present
        if (!empty($params['price_discount'])) {
            $conditions[] = $brandsQuery->newExpr()->gte(
                $brandsQuery->newExpr()->add('((Products.sales_price - Products.promotion_price)*100/Products.sales_price)'),
                trim($params['price_discount'])
            );
        }

        // Handle attribute filters
        if (!empty($params['attribute_filter'])) {
            // If attribute_filter is a string, convert it to an array
            if (is_string($params['attribute_filter'])) {
                $attributeFilters = explode(',', $params['attribute_filter']);

                // Group attribute filters by attribute_id
                $attributeGroups = [];
                foreach ($attributeFilters as $attributeFilter) {
                    // Format is expected to be attribute_id-attribute_value_id
                    $parts = explode('-', $attributeFilter);
                    if (count($parts) === 2) {
                        $attributeId = $parts[0];
                        $attributeValueId = $parts[1];

                        // Group attribute values by attribute ID
                        if (!isset($attributeGroups[$attributeId])) {
                            $attributeGroups[$attributeId] = [];
                        }
                        $attributeGroups[$attributeId][] = $attributeValueId;
                    }
                }

                // Add a matching condition for each attribute group
                foreach ($attributeGroups as $attributeId => $attributeValueIds) {
                    $brandsQuery->innerJoin(
                        ['ProductAttributes' . $attributeId => 'product_attributes'],
                        [
                            'ProductAttributes' . $attributeId . '.product_id = Products.id',
                            'ProductAttributes' . $attributeId . '.attribute_id' => $attributeId,
                            'ProductAttributes' . $attributeId . '.attribute_value_id IN' => $attributeValueIds,
                            'ProductAttributes' . $attributeId . '.status' => 'A'
                        ]
                    );
                }
            }
        }

        // Join the necessary tables
        $brandsQuery->join([
            'ProductCategories' => [
                'table' => 'product_categories',
                'type' => 'LEFT',
                'conditions' => 'ProductCategories.product_id = Products.id'
            ],
            'ProductCategories2' => [
                'table' => 'product_categories',
                'type' => 'LEFT',
                'conditions' => ['ProductCategories2.product_id = Products.id', 'ProductCategories2.level IN' => [1, 2]]
            ],
            'Brands' => [
                'table' => 'brands',
                'type' => 'INNER',
                'conditions' => 'Products.brand_id = Brands.id'
            ]
        ]);

        // Apply the conditions
        $brandsQuery->where($conditions);

        // Select only brand id and name, and make sure they're distinct
        $brandsQuery->select(['id' => 'Brands.id', 'name' => 'Brands.name'])
            ->distinct(['Brands.id'])
            ->order(['Brands.name' => 'ASC']);

        // Execute the query and return the results
        return $brandsQuery->toArray();
    }

    /*****  Product List END *****/






































    //S
    public function productList($category, $sort, $brand, $price, $price_discount, $rating, $page, $limit, $attribute_filter, $filtercategory)
    {

        // Some changes done by M
        $page = (int)$page;
        $limit = (int)$limit;

        if ($sort == 'low-to-high') {
            $order = ['Products.promotion_price' => 'ASC'];
        } else if ($sort == 'high-to-low') {
            $order = ['Products.promotion_price' => 'DESC'];
        } else if ($sort == 'new-arrival') {
            $order = ['Products.created' => 'DESC'];
        } else if ($sort == 'relevance') {
            $order = ['Reviews.rating' => 'DESC'];
        } else if ($sort == 'discount') {
            $order = ['((Products.sales_price - Products.promotion_price)*100/Products.sales_price)' => 'DESC'];
        } else if ($sort == 'popularity') {
            $order = ['Reviews.rating' => 'DESC'];
        } else {
            $order = ['Reviews.rating' => 'DESC'];
        }


        $conditions = [];

        $conditions[] = [
            'Products.status' => 'A',
            'ProductCategories.category_id IN' => $category,
        ];

        if (!empty($filtercategory)) {
            $filtercategory_arr = explode("--", trim($filtercategory));
            $conditions[] = [
                'ProductCategories2.category_id IN' => $filtercategory_arr,
            ];
        }

        // Conditional filters based on user input

        if (!empty($brand)) {
            $brand_arr = explode("--", trim($brand));
            $conditions[] = ['Products.brand_id IN' => $brand_arr];
        }

        if (!empty($price)) {
            $priceRange = explode("--", trim($price));
            $conditions[] = ['Products.promotion_price >=' => $priceRange[0], 'Products.promotion_price <=' => $priceRange[1]];
        }

        if (!empty($price_discount)) {
            $conditions[] = ['((Products.sales_price - Products.promotion_price)*100/Products.sales_price)>=' . trim($price_discount)];
        }

        // Dynamic Attribute Filters
        /*if (!empty($attribute_filter)) {

            foreach ($attribute_filter as $key => $value) {
                // Assuming dynamic attributes are stored in ProductAttributes or a similar table
                $conditions[] = [
                    'CategoryAttributes.attribute_name' => $key,
                    'ProductAttributes.attribute_value' => $value
                ];
            }
        }*/

        $products = $this->find('all');
        $products->select($this);
        $query = $this->find();

        $caseExpression = $query->newExpr()->add(
            'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.offer_price
        ELSE Products.promotion_price
    END'
        );

        $StartDatecaseExpression = $query->newExpr()->add(
            'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.start_date
        ELSE Products.promotion_start_date
    END'
        );

        $EndDatecaseExpression = $query->newExpr()->add(
            'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.end_date
        ELSE Products.promotion_end_date
    END'
        );

        $isDealActiveExpression = $query->newExpr()->add(
            'CASE
                WHEN ProductDeals.status = "A"
                     AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
                THEN 1
                ELSE 0
            END'
        );

        $products->select([
            'Brands.name',
            'details' => 'Products.details',
            'product_discount' => $products->newExpr()->add('ROUND(Products.sales_price - Products.promotion_price, 2)'),
            'avg_rating' => $products->newExpr()->add('ROUND(AVG(Reviews.rating), 1)'),

            // Count distinct users who gave each rating (not total reviews)
            'rating_1_count' => $products->newExpr()->add('COUNT(DISTINCT CASE WHEN Reviews.rating = 1 THEN Reviews.customer_id END)'),
            'rating_2_count' => $products->newExpr()->add('COUNT(DISTINCT CASE WHEN Reviews.rating = 2 THEN Reviews.customer_id END)'),
            'rating_3_count' => $products->newExpr()->add('COUNT(DISTINCT CASE WHEN Reviews.rating = 3 THEN Reviews.customer_id END)'),
            'rating_4_count' => $products->newExpr()->add('COUNT(DISTINCT CASE WHEN Reviews.rating = 4 THEN Reviews.customer_id END)'),
            'rating_5_count' => $products->newExpr()->add('COUNT(DISTINCT CASE WHEN Reviews.rating = 5 THEN Reviews.customer_id END)')
        ])
            ->contain([
                'ProductCategories' => [
                    'Categories'
                ],
                'ProductDeals'
            ])
            ->join([
                'Brands' => [
                    'table' => 'brands',
                    'type' => 'INNER',
                    'conditions' => 'Products.brand_id = Brands.id'
                ],
                'ProductCategories' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => 'ProductCategories.product_id = Products.id '
                ],
                'ProductCategories2' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => 'ProductCategories2.product_id = Products.id and ProductCategories2.level IN (1,2)'
                ],
                'Reviews' => [
                    'table' => 'reviews',
                    'type' => 'LEFT',
                    'conditions' => 'Reviews.product_id = Products.id'
                ],
                'ProductDeals' => [
                    'table' => 'product_deals',
                    'type' => 'LEFT',
                    'conditions' => [
                        'ProductDeals.product_id = Products.id',
                        'ProductDeals.status = "A"',
                        'CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date'
                    ]
                ]
                // 'Suppliers' => [
                //     'table' => 'suppliers',
                //     'type' => 'LEFT',
                //     'conditions' => 'Products.supplier_id = Suppliers.id'
                // ]
            ])
            ->where($conditions)
            ->order($order)
            ->group(['Products.id']); // Group by product ID to calculate the average correctly

        if (!empty($rating)) {
            $products->having(function (QueryExpression $exp) use ($rating) {
                return $exp->gte('Reviews.rating', $rating); // Filter products with average rating >= rating
            });
        }

        $products->page($page, $limit);

        $result = $products->toArray();

        if (!$result) {
            return null;
        }

        return $result;
    }

    //M
    public function getSimilarProducts($productId)
    {
        $relatedProducts = [];
        if ($productId) {
            $relatedProductIds = [];
            $relatedProducts = $this->RelatedProducts->find()
                ->select(['related_id'])
                ->where(['status' => 'A', 'product_id' => $productId]);

            foreach ($relatedProducts as $relatedProduct) {
                $relatedProductIds[] = $relatedProduct->related_id;
            }

            // if ($productId) {
            //     $relatedProductIds[] = $productId;
            // }

            $query = $this->find();

            $caseExpression = $query->newExpr()->add(
                'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.offer_price
        ELSE Products.promotion_price
    END'
            );

            $StartDatecaseExpression = $query->newExpr()->add(
                'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.start_date
        ELSE Products.promotion_start_date
    END'
            );

            $EndDatecaseExpression = $query->newExpr()->add(
                'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.end_date
        ELSE Products.promotion_end_date
    END'
            );

            $isDealActiveExpression = $query->newExpr()->add(
                'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN 1
        ELSE 0
    END'
            );
            if ($relatedProductIds) {
                $relatedProducts = $this->find()
                    ->select([
                        'Products.id',
                        'Products.name',
                        'Brands.name',
                        'Reviews.rating',
                        'promotion_price' => $caseExpression,
                        'promotion_start_date' => $StartDatecaseExpression,
                        'promotion_end_date' => $EndDatecaseExpression,
                        'IsDealActive' => $isDealActiveExpression,
                    ])
                    ->contain([
                        'ProductCategories' => [
                            'Categories'
                        ],
                        'ProductDeals'
                    ])
                    ->join([
                        'Brands' => [
                            'table' => 'brands',
                            'type' => 'INNER',
                            'conditions' => 'Products.brand_id = Brands.id'
                        ],
                        'ProductCategories' => [
                            'table' => 'product_categories',
                            'type' => 'LEFT',
                            'conditions' => 'ProductCategories.product_id = Products.id'
                        ],
                        'ProductCategories2' => [
                            'table' => 'product_categories',
                            'type' => 'LEFT',
                            'conditions' => 'ProductCategories2.product_id = Products.id AND ProductCategories2.level IN (1,2)'
                        ],
                        'Reviews' => [
                            'table' => 'reviews',
                            'type' => 'LEFT',
                            'conditions' => 'Reviews.product_id = Products.id'
                        ],
                        'ProductDeals' => [
                            'table' => 'product_deals',
                            'type' => 'LEFT',
                            'conditions' => 'ProductDeals.product_id = Products.id'
                        ]
                    ])
                    ->where(['Products.id IN' => $relatedProductIds])
                    ->where(['Products.status' => 'A'])
                    ->group(['Products.id'])
                    ->toArray();
            }
        }

        return $relatedProducts;
    }

    //M Not In Use
    public function similarProduct($productId)
    {

        $tableLocator = new TableLocator();
        $productCategoriesTable = $tableLocator->get('ProductCategories');
        $categoriesTable = $tableLocator->get('Categories');

        $productCategoryIds = $productCategoriesTable->find('list', [
            'keyField' => 'category_id',
            'valueField' => 'category_id'
        ])
            ->where(['product_id' => $productId])
            ->toArray();

        if (empty($productCategoryIds)) {
            return [];
        }

        $lastLevelCategory = $categoriesTable->find('all')
            ->where([
                'id IN' => $productCategoryIds,
                'id NOT IN' => $categoriesTable->find('list', ['keyField' => 'id', 'valueField' => 'id'])
                    ->where(['parent_id IN' => $productCategoryIds])
            ])
            ->first();


        if (empty($lastLevelCategory)) {
            return [];
        }

        $similarProducts = $this->find('all')
            ->matching('ProductCategories', function ($q) use ($lastLevelCategory) {
                return $q->where(['ProductCategories.category_id' => $lastLevelCategory->id]);
            })
            ->where(['Products.id !=' => $productId])
            ->limit(10)
            ->toArray();

        return !empty($similarProducts) ? $similarProducts : [];
    }

    //M
    public function productView($productId, $customerId = null, $guestToken = null)
    {
        $conditions = [
            'Products.status' => 'A',
            'Products.id' => $productId,
            'Products.country_id' => $this->country_id,
            'Products.approval_status' => 'Approved',
        ];

        $products = $this->find();
        $products->select($this);
        $query = $this->find();

        $isArabic = ($this->language === 'ar' || $this->language === 'Arabic' || strtolower($this->language) === 'arabic');
        $nameField = $isArabic ? 'COALESCE(Products.name_ar, Products.name)' : 'Products.name';
        $detailsField = $isArabic ? 'COALESCE(Products.details_ar, Products.details)' : 'Products.details';
        $featureField = $isArabic ? 'COALESCE(Products.features_ar, Products.features)' : 'Products.features';
        $descriptionField = $isArabic ? 'COALESCE(Products.description_ar, Products.description)' : 'Products.description';
        $products->select([
            'Brands.name',
            'name' => $products->newExpr()->add($nameField),
            'details' => $products->newExpr()->add($detailsField),
            'features' => $products->newExpr()->add($featureField),
            'description' => $products->newExpr()->add($descriptionField),
            'product_discount' => $products->newExpr()->add('ROUND(Products.sales_price - Products.promotion_price, 2)'),
            'avg_rating' => $products->newExpr()->add('ROUND(AVG(Reviews.rating), 1)'),

            // Count distinct users who gave each rating (not total reviews)
            'rating_1_count' => $products->newExpr()->add('COUNT(DISTINCT CASE WHEN Reviews.rating = 1 THEN Reviews.customer_id END)'),
            'rating_2_count' => $products->newExpr()->add('COUNT(DISTINCT CASE WHEN Reviews.rating = 2 THEN Reviews.customer_id END)'),
            'rating_3_count' => $products->newExpr()->add('COUNT(DISTINCT CASE WHEN Reviews.rating = 3 THEN Reviews.customer_id END)'),
            'rating_4_count' => $products->newExpr()->add('COUNT(DISTINCT CASE WHEN Reviews.rating = 4 THEN Reviews.customer_id END)'),
            'rating_5_count' => $products->newExpr()->add('COUNT(DISTINCT CASE WHEN Reviews.rating = 5 THEN Reviews.customer_id END)')
        ])
            ->contain([
                'ProductImages' => function ($q) {
                    return $q->where(['ProductImages.status' => 'A']);
                },
                'ProductAttributes' => function ($q) {
                    return $q->where(['ProductAttributes.status' => 'A']);
                },
                'ProductVariants' => function ($q) {
                    return $q->select([
                        'ProductVariants.id',
                        'ProductVariants.product_id',
                        'ProductVariants.variant_name',
                        'ProductVariants.reference_name',
                        'ProductVariants.sku',
                        'ProductVariants.variant_size',
                        'ProductVariants.variant_weight',
                        'ProductVariants.purchase_price',
                        'ProductVariants.sales_price',
                        'ProductVariants.promotion_price',
                        'ProductVariants.quantity',
                        'ProductVariants.variant_description',
                        'ProductVariants.status',
                        'product_variant_discount' => $q->newExpr()->add('ProductVariants.sales_price - ProductVariants.promotion_price')
                    ])->where(['ProductVariants.status' => 'A']);
                },

                'ProductVariants.ProductVariantImages' => function ($q) {
                    return $q->where(['ProductVariantImages.status' => 'A']);
                },
                'ProductCategories' => [
                    'Categories' => function ($q) {
                        return $q->select(['id', 'name', 'url_key']);
                    }
                ],
                'Reviews' => function ($q) {
                    return $q->select(['id', 'product_id', 'rating', 'comment', 'status', 'customer_id'])
                        ->where(['Reviews.status' => 'A'])
                        ->contain([
                            'Customers' => function ($q2) {
                                return $q2->select(['id', 'profile_photo'])->contain([
                                    'Users' => function ($q3) {
                                        return $q3->select(['id','first_name', 'last_name']);
                                    }
                                ]);
                            }
                        ]);
                }
            ])
            ->join([
                'Brands' => [
                    'table' => 'brands',
                    'type' => 'INNER',
                    'conditions' => 'Products.brand_id = Brands.id'
                ],
                'Reviews' => [
                    'table' => 'reviews',
                    'type' => 'LEFT',
                    'conditions' => [
                        'Reviews.product_id = Products.id',
                        'Reviews.status' => 'A'
                    ]
                ],
                'ProductAttributes' => [
                    'table' => 'product_attributes',
                    'type' => 'LEFT',
                    'conditions' => ['ProductAttributes.product_id = Products.id', 'ProductAttributes.status' => 'A']
                ],
                'ProductVariants' => [
                    'table' => 'product_variants',
                    'type' => 'LEFT',
                    'conditions' => ['ProductVariants.product_id = Products.id', 'ProductVariants.status' => 'A']
                ]
                // 'Suppliers' => [
                //     'table' => 'suppliers',
                //     'type' => 'LEFT',
                //     'conditions' => 'Products.supplier_id = Suppliers.id'
                // ]
            ])
            ->where($conditions)
            ->group(['Products.id']);

        $result = $products->first();

        if (!$result) {
            return null;
        }

        // Add cart and wishlist information if customer/guest data is provided
        if ($customerId !== null || $guestToken !== null) {
            $this->addCartAndWishlistInfo($result, $productId, $customerId, $guestToken);
        }

        return $result;
    }

    /**
     * Add cart and wishlist information to product data
     *
     * @param object $productData Product data object
     * @param int $productId Product ID
     * @param int|null $customerId Customer ID (for logged-in users)
     * @param string|null $guestToken Guest token (for guest users)
     * @return void
     */
    private function addCartAndWishlistInfo($productData, $productId, $customerId = null, $guestToken = null)
    {
        // Load required tables
        $tableLocator = new \Cake\ORM\Locator\TableLocator();
        $cartsTable = $tableLocator->get('Carts');
        $cartItemsTable = $tableLocator->get('CartItems');
        $wishlistsTable = $tableLocator->get('Wishlists');

        // Check wishlist status (for both customer and guest users)
        $productData->whishlist = $wishlistsTable->isInWishlist($customerId, $guestToken, $productId);

        // Initialize cart variables
        $cartItemQuantity = 0;
        $isInCart = false;
        $cart_installation_charge =0;

        // Build cart conditions
        $cartConditions = [];
        if ($customerId) {
            $cartConditions['customer_id'] = $customerId;
        } elseif ($guestToken !== null) {
            $cartConditions['guest_token'] = $guestToken;
        }

        // Get cart data if conditions exist
        if (!empty($cartConditions)) {
            $cart = $cartsTable->find()
                ->where($cartConditions)
                ->contain(['CartItems' => function ($q) use ($productId) {
                    return $q->where(['CartItems.product_id' => $productId]);
                }])
                ->first();

            if ($cart && !empty($cart->cart_items)) {
                $cartItem = $cart->cart_items[0]; // Get the first (and should be only) cart item for this product
                $cartItemQuantity = $cartItem->quantity;
                $isInCart = true;
                $cart_installation_charge = $cartItem->installation_charge;
            }
        }

        // Add cart information to product data
        $productData->cart_quantity = $cartItemQuantity;
        $productData->is_in_cart = $isInCart;
        $productData->cart_installation_charge=$cart_installation_charge;
    }

    public function getProductPrice($productId)
    {
        //        $productPrice = $this->Products->getProductPrice($productId);
        //        echo 'Product Price: ' . ($productPrice ?? 'N/A');

        $query = $this->find();

        // CASE expression to determine the correct price
        $priceExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN ProductDeals.offer_price
            ELSE Products.promotion_price
        END'
        );

        // Query to fetch price based on conditions
        $product = $this->find()
            ->select([
                'price' => $priceExpression,
            ])
            ->leftJoin(
                ['ProductDeals' => 'product_deals'],
                [
                    'ProductDeals.product_id = Products.id',
                    'ProductDeals.status' => 'A',
                    'CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date'
                ]
            )
            ->where([
                'Products.id' => $productId,
                'Products.status' => 'A'
            ])
            ->first();

        return $product ? (float) $product->price : null;
    }

    public function getDiscount($productId)
    {
        $product = $this->find()
            ->select([
                'discount' => 'ROUND((sales_price - promotion_price) * 100 / sales_price, 2)'
            ])
            ->where(['id' => $productId])
            ->first();

        return $product ? (float)$product->discount : 0.0; // Return 0.0 if no product found
    }

    public function getDiscountProduct($productId, $variant = null)
    {
        if ($variant) {
            // Check in product_variants table if variant is provided
            $productVariant = $this->ProductVariants->find()
                ->select([
                    'discount' => 'ROUND((sales_price - promotion_price) * 100 / sales_price, 2)'
                ])
                ->where(['product_id' => $productId, 'id' => $variant])
                ->first();

            return $productVariant ? (float)$productVariant->discount : 0.0;
        } else {
            // Check in products table if no variant is provided
            $product = $this->find()
                ->select([
                    'discount' => 'ROUND((sales_price - promotion_price) * 100 / sales_price, 2)'
                ])
                ->where(['id' => $productId])
                ->first();

            return $product ? (float)$product->discount : 0.0;
        }
    }

    public function getAvailabilityStatus($productId)
    {
        $product = $this->find()
            ->select(['availability_status' => 'IF((SELECT SUM(quantity) FROM product_stocks WHERE product_id = Products.id) > 0, "In Stock", "Out of Stock")'])
            ->where(['id' => $productId])
            ->first();

        return $product ? $product->availability_status : 'Out of Stock'; // Default to 'Out of Stock' if no product found
    }

    //S
    // public function productView($productId) {

    //     $conditions = [];

    //     // Base conditions that always apply
    //     $conditions[] = [
    //         'Products.status' => 'A',
    //         'Products.id' => $productId,
    //         'Reviews.status'=>'A'
    //     ];

    //     $products = $this->find();
    //     $products->select($this)
    //         ->select([
    //             'Brands.name',
    //             'rating' => $products->func()->avg('Reviews.rating'),
    //             'total_reviews' => $products->func()->count('Reviews.id'),
    //             'discount' => $products->newExpr()->add('ROUND((Products.product_price - Products.promotion_price)*100/Products.product_price,2)')
    //         ])
    //         ->contain([ 'ProductImages','ProductCategories' ]) // Contain any necessary associations
    //         ->join([
    //             'Brands' => [
    //             'table' => 'brands',
    //             'type' => 'INNER',
    //             'conditions' => 'Products.brand_id = Brands.id'
    //             ],
    //             'Reviews' => [
    //             'table' => 'reviews',
    //             'type' => 'LEFT',
    //             'conditions' => 'Reviews.product_id = Products.id'
    //             ]
    //         ])
    //         ->where($conditions)
    //         ->group(['Products.id']) // Group by product ID to calculate the average correctly
    //         ->first();

    //     echo "<pre>"; print_r($products->toArray()); die;
    //     return $products;

    // }

    //S
    // public function similarProduct($productId) {

    //     $tableLocator = new TableLocator();
    //     $productCategoriesTable = $tableLocator->get('ProductCategories');
    //     $categoriesTable = $tableLocator->get('Categories');

    //     // Get all categories associated with the product
    //     $productCategoryIds = $productCategoriesTable->find('list', [
    //             'keyField' => 'category_id',
    //             'valueField' => 'category_id'
    //         ])
    //         ->where(['product_id' => $productId])
    //         ->toArray();

    //     // Find the last-level category among them
    //     $lastLevelCategory = $categoriesTable->find('all')
    //         ->where([
    //             'id IN' => $productCategoryIds,
    //             'id NOT IN' => $categoriesTable->find('list', ['keyField' => 'id', 'valueField' => 'id'])
    //                 ->where(['parent_id IN' => $productCategoryIds]) // Exclude categories that are parents (not leaf)
    //         ])
    //         ->first();

    //     $similarProducts = $this->find('all')
    //         ->matching('ProductCategories', function ($q) use ($lastLevelCategory) {
    //             return $q->where(['ProductCategories.category_id' => $lastLevelCategory->id]);
    //         })
    //         ->where(['Products.id !=' => $productId]) // Exclude the current product
    //         ->limit(10) // Optionally limit the number of results
    //         ->toArray();

    //     return $similarProducts;
    // }

    //S
    public function getProductsSuggestions($query)
    {
        return $this->find('all')
            ->select(['id', 'name', 'type' => "'product'"])
            ->where(['name LIKE' => '%' . $query . '%'])
            ->limit(10)
            ->toArray();
    }

    //S
    public function search($query)
    {

        // $products = $this->find()
        //     ->contain([
        //         'Brands',
        //     ])
        //     ->leftJoinWith('ProductCategories.Categories') // Join categories through ProductCategories
        //     ->where([
        //         'OR' => [
        //             'Products.name LIKE' => '%' . $query . '%', // Search in product names
        //             'Brands.name LIKE' => '%' . $query . '%', // Search in brand names
        //             'Categories.name LIKE' => '%' . $query . '%', // Search in category names
        //         ]
        //     ])
        //     ->distinct(['Products.id']) // Ensure unique results
        //     ->toArray();

        $queryReceived = $query;
        $queryNormalized = '';
        $queryParts = [];
        $lastThree = '';

        if (isset($query) && $query !== '') {
            $queryNormalized = strtolower(str_replace(' ', '', $queryReceived));
            $queryParts = explode(' ', strtolower($queryReceived));
            $lastThree = substr($queryNormalized, -3);
        }

        $productsByName = $this->find()
            ->contain(['Brands'])
            ->where([
                'Products.status' => 'A',
                'OR' => array_merge(
                    [
                        'LOWER(REPLACE(Products.name, " ", "")) LIKE' => '%' . $queryNormalized . '%',
                        'LOWER(REPLACE(Products.sku, " ", "")) LIKE' => '%' . $queryNormalized . '%',
                        'LOWER(REPLACE(Products.reference_name, " ", "")) LIKE' => '%' . $queryNormalized . '%',
                        'LOWER(REPLACE(Products.product_tags, " ", "")) LIKE' => '%' . $queryNormalized . '%',
                    ],
                    array_map(
                        fn($part) => [
                            'LOWER(Products.name) LIKE' => '%' . $part . '%',
                            'LOWER(Products.sku) LIKE' => '%' . $part . '%',
                            'LOWER(Products.reference_name) LIKE' => '%' . $part . '%',
                            'LOWER(Products.product_tags) LIKE' => '%' . $part . '%',
                        ],
                        $queryParts
                    ),
                    $lastThree ? [
                        ['LOWER(Products.name) LIKE' => '%' . $lastThree . '%'],
                        ['LOWER(Products.sku) LIKE' => '%' . $lastThree . '%'],
                        ['LOWER(Products.reference_name) LIKE' => '%' . $lastThree . '%'],
                        ['LOWER(Products.product_tags) LIKE' => '%' . $lastThree . '%'],
                    ] : []
                )
            ])
            ->distinct(['Products.id'])
            ->toArray();

        $productsByCategory = $this->find()
            ->contain(['Brands'])
            ->leftJoinWith('ProductCategories.Categories')
            ->where([
                'Products.status' => 'A',
                'Categories.status' => 'A',
                'OR' => array_merge(
                    ['LOWER(REPLACE(Categories.name, " ", "")) LIKE' => '%' . $queryNormalized . '%'],
                    array_map(
                        fn($part) => ['LOWER(Categories.name) LIKE' => '%' . $part . '%'],
                        $queryParts
                    ),
                    $lastThree ? [['LOWER(Categories.name) LIKE' => '%' . $lastThree . '%']] : []
                )
            ])
            ->distinct(['Products.id'])
            ->toArray();

        $productsByBrand = $this->find()
            ->contain(['Brands'])
            ->where([
                'Products.status' => 'A',
                'Brands.status' => 'A',
                'OR' => array_merge(
                    ['LOWER(REPLACE(Brands.name, " ", "")) LIKE' => '%' . $queryNormalized . '%'],
                    array_map(
                        fn($part) => ['LOWER(Brands.name) LIKE' => '%' . $part . '%'],
                        $queryParts
                    ),
                    $lastThree ? [['LOWER(Brands.name) LIKE' => '%' . $lastThree . '%']] : []
                )
            ])
            ->distinct(['Products.id'])
            ->toArray();


        $products = array_unique(
            array_merge($productsByName, $productsByCategory, $productsByBrand),
            SORT_REGULAR
        );

        return $products;
    }
    // Ax
    public function searchWebAPI($query)
    {
        $query = trim(strtolower($query));
        $queryParts = explode(' ', $query);

        $conditions = [
            'LOWER(Products.name) LIKE' => "%$query%",
            'LOWER(Products.sku) LIKE' => "%$query%",
            'LOWER(Products.reference_name) LIKE' => "%$query%",
            'LOWER(Products.product_tags) LIKE' => "%$query%",
        ];

        foreach ($queryParts as $part) {
            $conditions[] = ['LOWER(Products.name) LIKE' => "%$part%"];
            $conditions[] = ['LOWER(Products.sku) LIKE' => "%$part%"];
            $conditions[] = ['LOWER(Products.reference_name) LIKE' => "%$part%"];
            $conditions[] = ['LOWER(Products.product_tags) LIKE' => "%$part%"];
        }

        $products = $this->find()
            ->contain(['Brands'])
            ->where([
                'Products.status' => 'A',
                'OR' => $conditions,
            ])
            ->order([
                'CASE
                    WHEN LOWER(Products.name) = :query THEN 1
                    WHEN LOWER(Products.name) LIKE :query_full THEN 2
                    WHEN LOWER(Products.name) LIKE :query_partial THEN 3
                    ELSE 4
                 END' => 'ASC',
                'Products.id' => 'ASC'
            ])
            ->bind(':query', $query, 'string')
            ->bind(':query_full', "%$query%", 'string')
            ->bind(':query_partial', "%{$queryParts[0]}%", 'string')
            ->distinct(['Products.id'])
            ->toArray();

        return $products;
    }





    //M needs to clarify
    public function getDealOfTheDayProducts($limit = null, $categoryIds = [], $product_preference = null)
    {

        $today = FrozenTime::now();

        $query = $this->find();
        $query->select($this)
            ->select([
                'hours_left' => $this->find()->newExpr()->add('IF(Products.promotion_end_date IS NOT NULL, GREATEST(TIMESTAMPDIFF(HOUR, \'' . $today->format('Y-m-d H:i:s') . '\', DATE_ADD(Products.promotion_end_date, INTERVAL 1 DAY) - INTERVAL 1 SECOND), 0), NULL)')
            ])
            ->join([
                'Reviews' => [
                    'table' => 'reviews',
                    'type' => 'LEFT',
                    'conditions' => [
                        'Reviews.product_id = Products.id',
                        'Reviews.status' => 'A'
                    ]
                ],
                'ProductVariants' => [
                    'table' => 'product_variants',
                    'type' => 'LEFT',
                    'conditions' => 'ProductVariants.product_id = Products.id'
                ],
            ])
            ->where([
                'Products.status' => 'A',
                'Products.approval_status' => 'Approved',
                'Products.product_preference' => 'Deal',
            ]);

        if (!empty($categoryIds)) {
            $query->innerJoinWith('ProductCategories', function ($q) use ($categoryIds) {
                return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
            });
        }

        if ($limit !== null) {
            $query->limit($limit);
        }

        $query
            ->group(['Products.id'])
            ->order(['(Products.product_price - Products.promotion_price) * 100 / Products.product_price' => 'DESC'])
            ->toArray();

        return $query;
    }

    //M
    public function getTopSellingProducts($limit = null, $categoryIds = [], $product_preference = null)
    {
        $query = $this->find();

        $caseExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN ProductDeals.offer_price
            ELSE Products.promotion_price
         END'
        );

        $isDealActiveExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN 1
            ELSE 0
         END'
        );

        $query->select($this)
            ->select([
                'total_sales' => $this->find()->func()->count('OrderItems.id'),
                'promotion_price' => $caseExpression,
                'IsDealActive' => $isDealActiveExpression,
            ])
            ->join([
                'OrderItems' => [
                    'table' => 'order_items',
                    'type' => 'INNER',
                    'conditions' => 'OrderItems.product_id = Products.id',
                    'OrderItems.status NOT IN' => ['Cancelled', 'Returned'],
                ]
            ])
            ->leftJoinWith('Reviews', function ($q) {
                return $q->where(['Reviews.status' => 'A']);
            })
            ->leftJoinWith('ProductVariants')
            ->leftJoin(
                ['ProductDeals' => 'product_deals'],
                [
                    'ProductDeals.product_id = Products.id'
                ]
            )
            ->where([
                'Products.status' => 'A',
                'Products.approval_status' => 'Approved'
            ]);

        if (!empty($categoryIds)) {
            $query->innerJoinWith('ProductCategories', function ($q) use ($categoryIds) {
                return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
            });
        }

        if ($limit !== null) {
            $query->limit($limit);
        }

        $query->group(['Products.id'])
            ->order(['total_sales' => 'DESC'])
            ->toArray();

        return $query;
    }

    //M
    public function getNewArrivalProducts($limit = null, $categoryIds = [], $product_preference = null)
    {
        $query = $this->find();

        // Define CASE expressions for promotion price and deal active flag
        $caseExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN ProductDeals.offer_price
            ELSE Products.promotion_price
         END'
        );
        $isDealActiveExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN 1
            ELSE 0
         END'
        );

        $query->select($this)
            ->select([
                'promotion_price' => $caseExpression,
                'IsDealActive' => $isDealActiveExpression,
            ])
            ->join([
                'Reviews' => [
                    'table' => 'reviews',
                    'type' => 'LEFT',
                    'conditions' => [
                        'Reviews.product_id = Products.id',
                        'Reviews.status' => 'A'
                    ]
                ],
                'ProductVariants' => [
                    'table' => 'product_variants',
                    'type' => 'LEFT',
                    'conditions' => 'ProductVariants.product_id = Products.id'
                ]
            ])
            // Left join with product_deals to allow promotion override logic
            ->leftJoin(
                ['ProductDeals' => 'product_deals'],
                ['ProductDeals.product_id = Products.id']
            )
            ->where([
                'Products.status' => 'A',
                'Products.approval_status' => 'Approved'
            ]);

        if (!empty($categoryIds)) {
            $query->innerJoinWith('ProductCategories', function ($q) use ($categoryIds) {
                return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
            });
        }

        if ($limit !== null) {
            $query->limit($limit);
        }

        $query->group(['Products.id'])
            ->order(['Products.created' => 'DESC']);

        $result = $query->toArray();

        if (!$result) {
            return null;
        }

        return $result;
    }


    //M
    public function getFeaturedProducts($limit = null, $categoryIds = [], $product_preference = null, $language = null)
    {
       
        // Determine if Arabic language is selected
        $isArabic = ($this->language === 'ar' || $this->language === 'Arabic' || strtolower($this->language) === 'arabic');

        // Select appropriate language fields
        $nameField = $isArabic ? 'COALESCE(Products.name_ar, Products.name)' : 'Products.name';
        $descriptionField = $isArabic ? 'COALESCE(Products.description_ar, Products.description)' : 'Products.description';

        $query = $this->find()
            ->select([
            'id',
            'name' => $this->find()->newExpr()->add($nameField),
            'description' => $this->find()->newExpr()->add($descriptionField),
            'url_key',
            'sales_price',
            'product_price',
            'promotion_price',
            'discount' => $this->find()->newExpr()->add('ROUND((Products.sales_price - Products.promotion_price) * 100 / Products.sales_price, 2)'),
            'product_image' => 'ProductImages.image',
            'avg_rating' => $this->find()->newExpr()->add('ROUND(AVG(Reviews.rating), 1)')
            ])
            ->contain([
            'Reviews' => function ($q) {
                return $q->select(['product_id', 'rating'])
                ->where(['Reviews.status' => 'A']);
            },
            'ProductCategories' => [
                'Categories' => function ($q) use ($isArabic) {
                    $categoryNameField = $isArabic ? 'COALESCE(Categories.name_ar, Categories.name)' : 'Categories.name';
                    return $q->select([
                        'id',
                        'name' => $q->newExpr()->add($categoryNameField),
                        'parent_id',
                        'url_key'
                    ])
                    ->where(['Categories.parent_id IS' => null]);
                }
            ],
            'ProductImages' => function ($q) {
                return $q->select(['product_id', 'image', 'media_type', 'image_default'])
                ->where([
                    'ProductImages.status' => 'A',
                    'ProductImages.image_default' => 1,
                    'ProductImages.media_type' => 'Image'
                ]);
            }
            ])
            ->leftJoin(
                ['Reviews' => 'reviews'],
                ['Reviews.product_id = Products.id', 'Reviews.status' => 'A']
            )
            ->leftJoin(
                ['ProductImages' => 'product_images'],
                [
                    'ProductImages.product_id = Products.id',
                    'ProductImages.status' => 'A',
                    'ProductImages.image_default' => 1,
                    'ProductImages.media_type' => 'Image'
                ]
            )
            ->where(['Products.status' => 'A', 'Products.approval_status' => 'Approved', 'Products.country_id' => $this->country_id])
            ->group(['Products.id'])
            ->limit($limit);

        return $query->toArray();
    }

    public function findProductByIdOrUrlKey($identifier)
    {
        $conditions = [];
        if (is_numeric($identifier)) {
            $conditions['OR'] = [
                'Products.id' => (int)$identifier
            ];
        } else {
            $conditions['Products.url_key'] = $identifier;
        }

        $query = $this->find()
            ->where($conditions)
            ->first();

        return $query;
    }

    //M
    public function getSpecialOffersProducts($limit = null, $categoryIds = [], $product_preference = null)
    {
        $query = $this->find();

        // Define CASE expressions for promotion price and deal active flag.
        $caseExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN ProductDeals.offer_price
            ELSE Products.promotion_price
         END'
        );
        $isDealActiveExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN 1
            ELSE 0
         END'
        );

        $query->select($this)
            ->select([
                'promotion_price' => $caseExpression,
                'IsDealActive'    => $isDealActiveExpression,
            ])
            ->join([
                'Reviews' => [
                    'table'      => 'reviews',
                    'type'       => 'LEFT',
                    'conditions' => [
                        'Reviews.product_id = Products.id',
                        'Reviews.status' => 'A'
                    ]
                ],
                'ProductVariants' => [
                    'table'      => 'product_variants',
                    'type'       => 'LEFT',
                    'conditions' => 'ProductVariants.product_id = Products.id'
                ]
            ])
            ->leftJoin(
                ['ProductDeals' => 'product_deals'],
                ['ProductDeals.product_id = Products.id']
            )
            ->where([
                'Products.status'          => 'A',
                'Products.approval_status' => 'Approved',
                // Special offer condition: discount should be at least 20%
                'ROUND((Products.product_price - Products.promotion_price) * 100 / Products.product_price, 2) >=' => 20
            ]);

        if (!empty($categoryIds)) {
            $query->innerJoinWith('ProductCategories', function ($q) use ($categoryIds) {
                return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
            });
        }

        if ($limit !== null) {
            $query->limit($limit);
        }

        $query->group(['Products.id']);

        return $query->toArray();
    }

    //S
    public function productAutocomplete($queryStr)
    {
        return $this->find('all')
            ->select(['id', 'name'])
            ->where([
                'OR' => [
                    'name LIKE' => '%' . $queryStr . '%', // Search in product names
                    'sku LIKE' => '%' . $queryStr . '%', // Search in sku
                ]
            ])
            ->toArray();
    }

    //S
    public function searchProduct($flag, $queryStr)
    {
        if ($flag == 'ID') {
            $products = $this->find()
                ->select(['id', 'name', 'url_key', 'description', 'sku', 'product_weight', 'product_size', 'product_price', 'sales_price', 'promotion_price'])
                ->contain([
                    'ProductAttributes' => [
                        'Attributes',
                        'AttributeValues'
                    ],
                    'ProductVariants'
                ])
                ->where(['Products.id' => $queryStr])
                ->toArray();
        } else {

            $products = $this->find()
                ->select(['id', 'name', 'url_key', 'description', 'sku', 'product_weight', 'product_size', 'product_price', 'sales_price', 'promotion_price'])
                ->contain([
                    'ProductAttributes' => [
                        'Attributes',
                        'AttributeValues'
                    ],
                    'ProductVariants'
                ])
                ->where([
                    'OR' => [
                        'Products.name LIKE' => '%' . $queryStr . '%', // Search in product names
                        'Products.sku LIKE' => '%' . $queryStr . '%', // Search in sku
                    ]
                ])
                ->distinct(['Products.id']) // Ensure unique results
                ->toArray();
        }

        return $products;
    }

    //S
    public function getProductDetail($code)
    {
        $res = $this->find()
            ->select(['id', 'name', 'url_key', 'description', 'sku', 'product_weight', 'product_size', 'product_price', 'sales_price', 'promotion_price'])
            ->contain([
                'ProductAttributes' => [
                    'Attributes',
                    'AttributeValues'
                ],
                'ProductVariants'
            ])
            ->where(['sku' => $code])
            ->first();

        if ($res) {
            return $res->toArray();
        } else {
            return false;
        }
    }

    public function deleteProduct(EntityInterface $product, array $options = []): bool
    {

        if ($this->delete($product)) {
            return true;
        }
        return false;
    }

    /**
     * Get featured products for homepage tab filtering by category
     */
    public function getHomepageFeaturedProducts($categoryId, $limit = 6, $language = 'English')
    {
        $isArabic = ($language === 'ar' || $language === 'Arabic' || strtolower($language) === 'arabic');
        $nameField = $isArabic ? 'COALESCE(Products.name_ar, Products.name)' : 'Products.name';
        $descriptionField = $isArabic ? 'COALESCE(Products.description_ar, Products.description)' : 'Products.description';

        $query = $this->find()
            ->select([
                'id',
                'name' => $this->find()->newExpr()->add($nameField),
                'description' => $this->find()->newExpr()->add($descriptionField),
                'url_key',
                'sales_price',
                'product_price',
                'promotion_price',
                'discount' => $this->find()->newExpr()->add('ROUND((Products.sales_price - Products.promotion_price) * 100 / Products.sales_price, 2)'),
                'product_image' => 'ProductImages.image',
                'avg_rating' => $this->find()->newExpr()->add('ROUND(AVG(Reviews.rating), 1)')
            ])
            ->contain([
                'Reviews' => function ($q) {
                    return $q->select(['product_id', 'rating'])
                        ->where(['Reviews.status' => 'A']);
                },
                'ProductCategories' => [
                    'Categories' => function ($q) use ($isArabic) {
                        $categoryNameField = $isArabic ? 'COALESCE(Categories.name_ar, Categories.name)' : 'Categories.name';
                        return $q->select([
                            'id',
                            'name' => $q->newExpr()->add($categoryNameField),
                            'parent_id',
                            'url_key'
                        ])
                        ->where(['Categories.parent_id IS' => null]);
                    }
                ],
                'ProductImages' => function ($q) {
                    return $q->select(['product_id', 'image', 'media_type', 'image_default'])
                        ->where([
                            'ProductImages.status' => 'A',
                            'ProductImages.image_default' => 1,
                            'ProductImages.media_type' => 'Image'
                        ]);
                }
            ])
            ->leftJoin(
                ['Reviews' => 'reviews'],
                ['Reviews.product_id = Products.id', 'Reviews.status' => 'A']
            )
            ->leftJoin(
                ['ProductImages' => 'product_images'],
                [
                    'ProductImages.product_id = Products.id',
                    'ProductImages.status' => 'A',
                    'ProductImages.image_default' => 1,
                    'ProductImages.media_type' => 'Image'
                ]
            );

        // Only join and filter by category if categoryId is provided
        if (!empty($categoryId)) {
            $query->join([
                'ProductCategories' => [
                    'table' => 'product_categories',
                    'type' => 'INNER',
                    'conditions' => 'ProductCategories.product_id = Products.id'
                ]
            ])
            ->order(['Products.id' => 'DESC'])
            ->where([
                'ProductCategories.category_id' => $categoryId
            ]);
        }

        $query->where([
            'Products.status' => 'A',
            'Products.approval_status' => 'Approved',
            'Products.country_id' => $this->country_id,
        ])
        ->group(['Products.id'])
        ->limit($limit);

        $products = $query->toArray();

        // Add image URL fallback if missing
        foreach ($products as &$product) {
            if (empty($product['product_image'])) {
                $product['product_image'] = '../../img/no-image.jpg';
            }
        }

        return $products;
    }
}

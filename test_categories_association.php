<?php
/**
 * Test script to verify Categories association is working
 */

// Include CakePHP bootstrap
require_once 'config/bootstrap.php';

use Cake\ORM\TableRegistry;

echo "<h1>Categories Association Test</h1>";

try {
    // Get Products table
    $productsTable = TableRegistry::getTableLocator()->get('Products');
    
    echo "<h2>1. Testing Categories Association</h2>";
    
    // Test if Categories association exists
    $associations = $productsTable->associations();
    $categoryAssociation = $associations->get('Categories');
    
    if ($categoryAssociation) {
        echo "✓ Categories association found<br>";
        echo "✓ Association type: " . get_class($categoryAssociation) . "<br>";
        echo "✓ Foreign key: " . $categoryAssociation->getForeignKey() . "<br>";
        echo "✓ Target foreign key: " . $categoryAssociation->getTargetForeignKey() . "<br>";
        echo "✓ Join table: " . $categoryAssociation->junction()->getTable() . "<br>";
    } else {
        echo "✗ Categories association not found<br>";
        echo "Available associations: " . implode(', ', array_keys($associations->toArray())) . "<br>";
    }
    
    echo "<h2>2. Testing Categories Table Access</h2>";
    
    // Test direct access to Categories table
    $categoriesTable = TableRegistry::getTableLocator()->get('Categories');
    $categoryCount = $categoriesTable->find()->count();
    echo "✓ Categories table accessible (Count: {$categoryCount})<br>";
    
    // Get some sample categories
    $sampleCategories = $categoriesTable->find()
        ->where(['status' => 'A'])
        ->limit(5)
        ->all();
    
    echo "✓ Sample categories found:<br>";
    foreach ($sampleCategories as $category) {
        echo "  - ID: {$category->id}, Name: {$category->name}<br>";
    }
    
    echo "<h2>3. Testing Categories Find List</h2>";
    
    // Test the find list method used in the controller
    $categoriesList = $categoriesTable->find('list', [
        'conditions' => ['status' => 'A', 'parent_id IS' => null]
    ])->order(['name' => 'ASC'])->all();
    
    echo "✓ Categories list method works<br>";
    echo "✓ Found " . count($categoriesList) . " parent categories<br>";
    
    foreach ($categoriesList as $id => $name) {
        echo "  - {$id}: {$name}<br>";
    }
    
    echo "<h2>4. Testing Category Lookup (Import Logic)</h2>";
    
    // Test category lookup logic used in import
    $testCategoryNames = ['Electronics', 'Home & Garden', 'Fashion', 'Sports'];
    
    foreach ($testCategoryNames as $categoryName) {
        $category = $categoriesTable->find()
            ->where(['name LIKE' => '%' . $categoryName . '%', 'status' => 'A'])
            ->first();
        
        if ($category) {
            echo "✓ Category '{$categoryName}' found (ID: {$category->id})<br>";
        } else {
            echo "✗ Category '{$categoryName}' not found<br>";
        }
    }
    
    echo "<h2>5. Testing Product-Category Relationship</h2>";
    
    // Test if we can access categories through a product
    $sampleProduct = $productsTable->find()
        ->contain(['Categories'])
        ->first();
    
    if ($sampleProduct) {
        echo "✓ Sample product found (ID: {$sampleProduct->id})<br>";
        echo "✓ Product name: {$sampleProduct->name}<br>";
        
        if (!empty($sampleProduct->categories)) {
            echo "✓ Product has " . count($sampleProduct->categories) . " categories:<br>";
            foreach ($sampleProduct->categories as $category) {
                echo "  - {$category->name}<br>";
            }
        } else {
            echo "⚠ Product has no categories assigned<br>";
        }
    } else {
        echo "✗ No products found<br>";
    }
    
    echo "<h2>Test Summary</h2>";
    echo "✅ Categories association test completed successfully!<br>";
    echo "✅ The import system should now work with Categories.<br>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Failed</h2>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<br><hr>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>

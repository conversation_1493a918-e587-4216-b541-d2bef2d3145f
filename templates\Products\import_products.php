<?php
/**
 * @var \App\View\AppView $this
 * @var string $title
 * @var array $brands
 * @var array $categories
 * @var array $countries
 */
?>

<?php $this->start('script'); ?>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script>
// Helper function to show alerts with fallback
function showAlert(title, message, type) {
    if (typeof swal !== 'undefined') {
        // Use SweetAlert if available
        return swal({
            title: title,
            text: message,
            icon: type,
            button: '<?= __("OK") ?>'
        });
    } else {
        // Fallback to browser alert
        alert(title + ': ' + message);
        return Promise.resolve();
    }
}

$(document).ready(function() {
    console.log('Import page loaded successfully');
    console.log('SweetAlert available:', typeof swal !== 'undefined');

    // Update current country display
    function updateCountryDisplay() {
        const countryText = $('#selectedCountryText').text().trim();
        const countryDisplay = $('#current-country-display');

        if (countryText.includes('All Countries') || countryText === '') {
            countryDisplay.removeClass('badge-success').addClass('badge-danger')
                .html('<i class="fas fa-exclamation-triangle"></i> <?= __("No Country Selected") ?>');
        } else {
            countryDisplay.removeClass('badge-danger').addClass('badge-success')
                .html('<i class="fas fa-flag"></i> ' + countryText);
        }
    }

    // Initial update
    updateCountryDisplay();

    // Update when country changes (if the dropdown changes)
    $(document).on('DOMSubtreeModified', '#selectedCountryText', function() {
        updateCountryDisplay();
    });

    // File upload handling
    $('#import-form').on('submit', function(e) {
        e.preventDefault();
        console.log('Form submitted');

        // Check if a specific country is selected
        const countryDropdownText = $('#selectedCountryText').text().trim();
        if (countryDropdownText.includes('All Countries') || countryDropdownText === '') {
            showAlert('<?= __("Error") ?>', '<?= __("Please select a specific country from the header dropdown before importing products.") ?>', 'error');
            return;
        }

        const fileInput = $('#import_file')[0];
        const file = fileInput.files[0];

        if (!file) {
            showAlert('<?= __("Error") ?>', '<?= __("Please select a file to upload.") ?>', 'error');
            return;
        }
        
        // Validate file type
        const allowedTypes = ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(csv|xlsx|xls)$/i)) {
            showAlert('<?= __("Error") ?>', '<?= __("Please select a valid CSV or Excel file.") ?>', 'error');
            return;
        }
        
        // Show progress
        $('#import-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> <?= __("Processing...") ?>');
        $('#progress-container').show();
        $('#progress-bar').css('width', '0%');
        
        // Create FormData
        const formData = new FormData(this);
        
        // Upload file
        $.ajax({
            url: '<?= $this->Url->build(['action' => 'processImport']) ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener("progress", function(evt) {
                    if (evt.lengthComputable) {
                        const percentComplete = evt.loaded / evt.total * 100;
                        $('#progress-bar').css('width', percentComplete + '%');
                    }
                }, false);
                return xhr;
            },
            success: function(response) {
                console.log('Import response:', response);
                $('#progress-bar').css('width', '100%');

                if (response.status === 'success' || response.status === 'warning') {
                    let message = response.message;
                    if (response.errors && response.errors.length > 0) {
                        message += '\n\nErrors:\n' + response.errors.slice(0, 10).join('\n');
                        if (response.errors.length > 10) {
                            message += '\n... and ' + (response.errors.length - 10) + ' more errors.';
                        }
                    }
                    
                    showAlert(
                        response.status === 'success' ? '<?= __("Success") ?>' : '<?= __("Warning") ?>',
                        message,
                        response.status === 'success' ? 'success' : 'warning'
                    ).then(function() {
                        if (response.status === 'success') {
                            window.location.href = '<?= $this->Url->build(['action' => 'index']) ?>';
                        }
                    });
                } else {
                    showAlert('<?= __("Error") ?>', response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Import error:', xhr, status, error);
                console.error('Response text:', xhr.responseText);
                showAlert('<?= __("Error") ?>', '<?= __("An error occurred during import. Please try again.") ?>', 'error');
            },
            complete: function() {
                $('#import-btn').prop('disabled', false).html('<i class="fas fa-upload"></i> <?= __("Import Products") ?>');
                setTimeout(function() {
                    $('#progress-container').hide();
                }, 2000);
            }
        });
    });
    
    // File input change handler
    $('#import_file').on('change', function() {
        const file = this.files[0];
        if (file) {
            $('#file-info').html(`
                <div class="alert alert-info mt-2">
                    <strong><?= __("Selected file:") ?></strong> ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)
                </div>
            `);
        } else {
            $('#file-info').html('');
        }
    });
});
</script>
<?php $this->end(); ?>

<div class="section-header">
    <h1><?= __("Product Import") ?></h1>
    <div class="section-header-breadcrumb">
        <div class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboard', 'action' => 'index']) ?>"><?= __("Dashboard") ?></a>
        </div>
        <div class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'index']) ?>"><?= __("Products") ?></a>
        </div>
        <div class="breadcrumb-item active"><?= __("Import") ?></div>
    </div>
</div>

<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>

<div class="section-body">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><?= __("Import Products from CSV/Excel") ?></h4>
                        <div class="card-header-action">
                            <div class="dropdown">
                                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-download"></i> <?= __("Download Sample") ?>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="<?= $this->Url->build(['action' => 'downloadSampleCsv']) ?>">
                                        <i class="fas fa-file-csv"></i> <?= __("CSV Format") ?>
                                    </a>
                                    <a class="dropdown-item" href="<?= $this->Url->build(['action' => 'downloadSampleExcel']) ?>">
                                        <i class="fas fa-file-excel"></i> <?= __("Excel Format") ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Country Selection Warning -->
                        <div class="alert alert-warning mb-4">
                            <h5><i class="fas fa-exclamation-triangle"></i> <?= __("Important: Country Selection Required") ?></h5>
                            <p class="mb-2">
                                <?= __("Before importing products, you must select a specific country from the header dropdown. All imported products will be assigned to the selected country. If 'All Countries' is selected, the import will fail.") ?>
                            </p>
                            <div class="mt-2">
                                <strong><?= __("Currently Selected:") ?></strong>
                                <span id="current-country-display" class="badge badge-info">
                                    <i class="fas fa-globe"></i> <?= __("Loading...") ?>
                                </span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <?= $this->Form->create(null, [
                                    'id' => 'import-form',
                                    'type' => 'file',
                                    'class' => 'needs-validation',
                                    'novalidate' => true
                                ]) ?>
                                
                                <div class="form-group mb-3">
                                    <label for="import_file" class="form-label"><?= __("Select Import File") ?> <span class="text-danger">*</span></label>
                                    <?= $this->Form->file('import_file', [
                                        'class' => 'form-control',
                                        'id' => 'import_file',
                                        'accept' => '.csv,.xlsx,.xls',
                                        'required' => true
                                    ]) ?>
                                    <small class="form-text text-muted">
                                        <?= __("Supported formats: CSV, Excel (.xlsx, .xls). Maximum file size: 10MB") ?>
                                    </small>
                                    <div id="file-info"></div>
                                </div>

                                <div id="progress-container" style="display: none;" class="form-group">
                                    <label><?= __("Import Progress") ?></label>
                                    <div class="progress">
                                        <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                             role="progressbar" style="width: 0%"></div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" id="import-btn" class="btn btn-primary">
                                        <i class="fas fa-upload"></i> <?= __("Import Products") ?>
                                    </button>
                                    <a href="<?= $this->Url->build(['action' => 'index']) ?>" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> <?= __("Back to Products") ?>
                                    </a>
                                </div>

                                <?= $this->Form->end() ?>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h5><?= __("Import Instructions") ?></h5>
                                    </div>
                                    <div class="card-body">
                                        <h6><?= __("Required Fields:") ?></h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success"></i> <?= __("Country (select from header dropdown)") ?></li>
                                            <li><i class="fas fa-check text-success"></i> <?= __("Supplier Reference Title") ?></li>
                                            <li><i class="fas fa-check text-success"></i> <?= __("Product Title") ?></li>
                                            <li><i class="fas fa-check text-success"></i> <?= __("Product Description") ?></li>
                                            <li><i class="fas fa-check text-success"></i> <?= __("Product Size") ?></li>
                                            <li><i class="fas fa-check text-success"></i> <?= __("Brand") ?></li>
                                        </ul>
                                        
                                        <h6><?= __("Optional Fields:") ?></h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-info text-info"></i> <?= __("Category & Sub Category") ?></li>
                                            <li><i class="fas fa-info text-info"></i> <?= __("Product Weight") ?></li>
                                            <li><i class="fas fa-info text-info"></i> <?= __("SKU (auto-generated if empty)") ?></li>
                                            <li><i class="fas fa-info text-info"></i> <?= __("Pricing Information") ?></li>
                                        </ul>

                                        <h6><?= __("Removed Fields:") ?></h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-times text-muted"></i> <?= __("COD in City (set to 'No' by default)") ?></li>
                                            <li><i class="fas fa-times text-muted"></i> <?= __("COD out City (set to 'No' by default)") ?></li>
                                            <li><i class="fas fa-times text-muted"></i> <?= __("Available on Credit (set to 'No' by default)") ?></li>
                                        </ul>
                                        
                                        <div class="alert alert-warning">
                                            <strong><?= __("Note:") ?></strong> <?= __("Download the sample file to see the exact format required.") ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.progress {
    height: 25px;
}

.progress-bar {
    font-size: 14px;
    line-height: 25px;
}

.card-header-action .dropdown-menu {
    right: 0;
    left: auto;
}
</style>

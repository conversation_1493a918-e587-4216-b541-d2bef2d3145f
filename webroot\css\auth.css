/* index.css */

body {
    font-family: '<PERSON>o';
    font-size: 22px;
}

#parent {
    position: absolute;
    width: 100%;
    /* height: 100vh; */
}

.navbar-brand-img {
    width: 500px;
}

.login-form {
    width: 100%;
}

.login-form .img-fluid {
    max-width: 100%;
    height: auto;
    margin: auto;
}

.message {
    position: fixed;
    left: 37%;
    bottom: -1000px;
    line-height: 115px;
    text-align: center;
    font-family: Arial, sans-serif;
    font-weight: bold;
    border-radius: 60px;
    animation: move 12s infinite linear;
}

.message:nth-child(2) {
    left: 120px;
    animation-delay: 2s;
}

.message:nth-child(3) {
    left: 240px;
    animation-delay: 4s;
}

#parent img {
    width: 120px;
}

@keyframes move {
    0% {
        bottom: -120px;
    }

    100% {
        bottom: 100%;
    }
}

/* .topnav {
    background: url("../app/assets/logo.png");
    padding-bottom: 65px;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
} */
.topnav.merchant {
    background: url(../img/ozone/admin-login-banner.png) no-repeat center top;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left top;
    width: 100%;
    height: calc(100vh - 0px);
}

.login-admin-form {
    margin: 7% auto 0%;
}

.store-card {
    padding: 30px 45px 40px;
    border-radius: 6px;
    box-shadow: 0 0 8px silver;
    margin: 22% auto 0px;
    background: #fff;
}

.store-card form {
    text-align: center;
}

.topnav .navbar-header {
    text-align: right;
}

.store-card .input-border {
    width: 100%;
    height: 40px;
    position: relative;
    margin-bottom: 30px;
}

.store-card .text {
    width: 100%;
    height: 100%;
    border: none;
    border-bottom: 1px solid #767676;
    background: none;
    font-size: medium;
    color: #767676;
}

.store-card border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: #fff;
    transition: .5s;
}

.store-card .text:focus~.border,
.text:valid~.border {
    width: 100%;
}

.store-card label {
    position: absolute;
    top: 8px;
    left: 3px;
    color: #767676;
    pointer-events: none;
    font-size: medium;
    transition: .5s;
    font-weight: 500;
}

input:required {
    box-shadow: none;
}

input:invalid {
    box-shadow: 0 0 1px transparent;
}

.store-card .text:focus~label,
.text:valid~label {
    top: -20px;
    left: 0;
    color: #121212;
    font-size: 14px;
}

.store-card h4 {
    color: #206331;
    font-weight: bold;
    font-size: 24px;
    margin-top: 0px;
    margin-bottom: 20px;
}

.store-card p {
    color: #696969;
    font-size: 18px;
    margin-bottom: 30px;
}

.store-card .btn {
    background: #6FC284;
    color: #fff;
    font-weight: bold;
    width: 100%;
    padding: 7px;
    font-size: 18px;
    border-radius: 9px;
}

.store-card .input-border a span {
    float: right;
    font-size: 14px;
    color: #757575;
    width: 100%;
    text-align: right;
    margin-top: 20px;
    margin-bottom: 40px;
}

.m-signup {
    font-size: 14px;
    color: #757575;
    text-align: center;
    width: 60%;
    display: block;
    margin: 0 auto;
    margin-top: 12px;
    margin-bottom: 20px;
}

.writeup-display {
    padding-top: 40px;
    padding-bottom: 40px;
    /* background-image: linear-gradient(to right, rgb(152, 104, 78), rgba(74, 47, 118, 1)); */
    background-image: linear-gradient(to right, #CEFFDA 0%, #00640000 40%, #00640000 40%);
}

.write-head h3 {
    color: #276100;
    text-transform: capitalize;
    font-weight: bold;
    font-size: 32px;
    margin-bottom: 26px;
}

.write-head p span {
    font-weight: 900;
    color: #000000;
    text-transform: capitalize;
    font-size: 24px;
}

.write-head p {
    font-weight: 500;
    color: #000000;
    font-size: 18px;
    width: 95%;
    float: right;
    line-height: 31px;
}

.footer-display {
    padding-top: 50px;
    padding-bottom: 80px;
    /* background-image: linear-gradient(to right, rgb(152, 104, 78), rgba(74, 47, 118, 1)); */
    background-image: linear-gradient(to right, #CEFFDA 0%, #00640000 40%, #00640000 40%);
}

.et-social-icon {
    display: inline-block;
}

.et-social-icons {
    padding: 0;
}

.et-social-icon a img {
    background: #206331;
    border-radius: 50%;
    padding: 2px;
}

.useful-links h3 {
    color: #000000;
    text-transform: capitalize;
    font-weight: bold;
    font-size: 24px;
    margin-bottom: 26px;
}

.copy-rights {
    color: #fff;
    font-size: 18px;
    line-height: 31px;
    margin-top: 24px;
}

.imp-links li {
    list-style: none;
}

.imp-links ul {
    padding: 0;
}

.imp-links a {
    color: #000000;
    font-size: 18px;
    line-height: 31px;
    margin-top: 24px;
    text-decoration: none;
}

.textwidget .address {
    color: #fff;
    line-height: 27px;
    font-size: 18px;
}

.textwidget .address a {
    color: #fff;
    line-height: 27px;
    font-size: 18px;
}


/* Responsive CSS starts here */
@media(max-width:1920px) {
    .topnav {
        /* padding-bottom: 371px; */
    }
}

@media(max-width:1700px) {
    .topnav {
        /* padding-bottom: 200px; */
    }
}

@media(max-width:1300px) {
    .topnav {
        /* padding-bottom: 65px; */
    }
}

@media(max-width:500px) {
    .store-card .input-border a span {
        width: 100%;
        text-align: right;
        position: relative;
        left: -15px;
    }

    .m-signup {
        width: 90%;
    }
}

.modal .modal-content .modal-body h1 {
    font-size: 26px;
    margin-top: 0;
}

.modal .modal-content .modal-body h5 {
    font-family: "GothamNarrowLight";
    font-size: 16px;
}

.modal .modal-content .modal-body .txt_msg {
    margin-top: 30px;
}

.modal .modal-content .modal-body #email_fld {
    font-family: "GothamNarrow";
    font-size: 20px;
    outline: none;
    border: 0;
    border-radius: unset;
    width: 100%;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

/* end of index.css */

/* -- Media Quiries -- */

@media only screen and (max-width:1200px) {
    .et-social-icon a img {
        height: 40px;
    }
}

@media only screen and (max-width:992px) {
    .et-social-icon a img {
        height: 30px;
    }

    .useful-links h3 {
        font-size: 20px;
    }
}

@media only screen and (max-width:768px) {
    .writeup-display {
        padding-bottom: 0;
    }

    .write-head p {
        width: 100%;
    }

    .store-card {
        padding: 30px;
    }
}

.store-card .frg-pass {
    margin-top: 60px;
}